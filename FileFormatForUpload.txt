12								
56								
1	SQLCHAR		0	50	","		1	Type
2	SQLCHAR		0	50	","		2	ForecastDate
3	SQLCHAR		0	50	","		3	ItemID
4	SQLCHAR		0	250	","		4	Name
5	SQLCHAR		0	500	","		5	ChangeNotes
6	SQLCHAR		0	50	","		6	Week1
7	SQLCHAR		0	50	","		7	Week2
8	SQLCHAR		0	50	","		8	Week3
9	SQLCHAR		0	50	","		9	Week4
10	SQLCHAR		0	50	","		10	Week5
11	SQLCHAR		0	50	","		11	Week6
12	SQLCHAR		0	50	","		12	Week7
13	SQLCHAR		0	50	","		13	Week8
14	SQLCHAR		0	50	","		14	Week9
15	SQLCHAR		0	50	","		15	Week10
16	SQLCHAR		0	50	","		16	Week11
17	SQLCHAR		0	50	","		17	Week12
18	SQLCHAR		0	50	","		18	Week13
19	SQLCHAR		0	50	","		19	Week14
20	SQLCHAR		0	50	","		20	Week15
21	SQLCHAR		0	50	","		21	Week16
22	SQLCHAR		0	50	","		22	Week17
23	SQLCHAR		0	50	","		23	Week18
24	SQLCHAR		0	50	","		24	Week19
25	SQLCHAR		0	50	","		25	Week20
26	SQLCHAR		0	50	","		26	Week21
27	SQLCHAR		0	50	","		27	Week22
28	SQLCHAR		0	50	","		28	Week23
29	SQLCHAR		0	50	","		29	Week24
30	SQLCHAR		0	50	","		30	Week25
31	SQLCHAR		0	50	","		31	Week26
32	SQLCHAR		0	50	","		32	Week27
33	SQLCHAR		0	50	","		33	Week28
34	SQLCHAR		0	50	","		34	Week29
35	SQLCHAR		0	50	","		35	Week30
36	SQLCHAR		0	50	","		36	Week31
37	SQLCHAR		0	50	","		37	Week32
38	SQLCHAR		0	50	","		38	Week33
39	SQLCHAR		0	50	","		39	Week34
40	SQLCHAR		0	50	","		40	Week35
41	SQLCHAR		0	50	","		41	Week36
42	SQLCHAR		0	50	","		42	Week37
43	SQLCHAR		0	50	","		43	Week38
44	SQLCHAR		0	50	","		44	Week39
45	SQLCHAR		0	50	","		45	Week40
46	SQLCHAR		0	50	","		46	Week41
47	SQLCHAR		0	50	","		47	Week42
48	SQLCHAR		0	50	","		48	Week43
49	SQLCHAR		0	50	","		49	Week44
50	SQLCHAR		0	50	","		50	Week45
51	SQLCHAR		0	50	","		51	Week46
52	SQLCHAR		0	50	","		52	Week47
53	SQLCHAR		0	50	","		53	Week48
54	SQLCHAR		0	50	","		54	Week49
55	SQLCHAR		0	50	","		55	Week50
56	SQLCHAR		0	50	","		56	Week51
	SQLCHAR		0	50	","			Week52
	SQLCHAR		0	50	\r\n			Week53
