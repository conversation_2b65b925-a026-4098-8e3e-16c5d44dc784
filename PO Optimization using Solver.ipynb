{"cells": [{"cell_type": "code", "execution_count": 4, "id": "33b8f6b3", "metadata": {}, "outputs": [], "source": ["from pyomo.environ import *\n", "import pandas as pd \n", "import numpy as np\n"]}, {"cell_type": "code", "execution_count": 5, "id": "e0d7563c", "metadata": {}, "outputs": [], "source": ["model = ConcreteModel()\n", "model.forecast_period = Set (initialize = ['2025-15', '2025-16', '2025-17', '2025-18', '2025-19', '2025-20', '2025-21', '2025-22', '2025-23', '2025-24', '2025-25'])\n", "# Create a subset for the last 8 periods (for variables only)\n", "model.variable_periods = Set (initialize = ['2025-19', '2025-20', '2025-21', '2025-22', '2025-23', '2025-24', '2025-25'])\n", "model.forecast_items  = Set (initialize = ['2389969', '2389980', '2389983', '2638384', '2638381'] )\n", "model.start_week = Set(initialize=['2025-15'])\n", "forecast_data = {\n", "            ( '2389969' , '2025-15' ) : 12,\n", "            ( '2389969' , '2025-16' ) : 12,\n", "            ( '2389969' , '2025-17' ) : 12,\n", "            ( '2389969' , '2025-18' ) : 12,\n", "            ( '2389969' , '2025-19' ) : 12,\n", "            ( '2389969' , '2025-20' ) : 12,\n", "            ( '2389969' , '2025-21' ) : 12,\n", "            ( '2389969' , '2025-22' ) : 12,\n", "            ( '2389969' , '2025-23' ) : 12,\n", "            ( '2389969' , '2025-24' ) : 12,\n", "            ( '2389980' , '2025-15' ) : 17,\n", "            ( '2389980' , '2025-16' ) : 17,\n", "            ( '2389980' , '2025-17' ) : 17,\n", "            ( '2389980' , '2025-18' ) : 17,\n", "            ( '2389980' , '2025-19' ) : 17,\n", "            ( '2389980' , '2025-20' ) : 17,\n", "            ( '2389980' , '2025-21' ) : 17,\n", "            ( '2389980' , '2025-22' ) : 17,\n", "            ( '2389980' , '2025-23' ) : 17,\n", "            ( '2389980' , '2025-24' ) : 17,\n", "            ( '2389983' , '2025-15' ) : 30,\n", "            ( '2389983' , '2025-16' ) : 30,\n", "            ( '2389983' , '2025-17' ) : 30,\n", "            ( '2389983' , '2025-18' ) : 30,\n", "            ( '2389983' , '2025-19' ) : 30,\n", "            ( '2389983' , '2025-20' ) : 30,\n", "            ( '2389983' , '2025-21' ) : 30,\n", "            ( '2389983' , '2025-22' ) : 30,\n", "            ( '2389983' , '2025-23' ) : 30,\n", "            ( '2389983' , '2025-24' ) : 30,\n", "            ( '2638384' , '2025-15' ) : 7,\n", "            ( '2638384' , '2025-16' ) : 7,\n", "            ( '2638384' , '2025-17' ) : 7,\n", "            ( '2638384' , '2025-18' ) : 7,\n", "            ( '2638384' , '2025-19' ) : 7,\n", "            ( '2638384' , '2025-20' ) : 7,\n", "            ( '2638384' , '2025-21' ) : 7,\n", "            ( '2638384' , '2025-22' ) : 7,\n", "            ( '2638384' , '2025-23' ) : 7,\n", "            ( '2638384' , '2025-24' ) : 7,\n", "            ( '2638381' , '2025-15' ) : 7,\n", "            ( '2638381' , '2025-16' ) : 7,\n", "            ( '2638381' , '2025-17' ) : 7,\n", "            ( '2638381' , '2025-18' ) : 7,\n", "            ( '2638381' , '2025-19' ) : 7,\n", "            ( '2638381' , '2025-20' ) : 7,\n", "            ( '2638381' , '2025-21' ) : 7,\n", "            ( '2638381' , '2025-22' ) : 7,\n", "            ( '2638381' , '2025-23' ) : 7,\n", "            ( '2638381' , '2025-24' ) : 7,\n", "            ( '2389969' , '2025-25' ) : 12,\n", "            ( '2389980' , '2025-25' ) : 17,\n", "            ( '2389983' , '2025-25' ) : 30,\n", "            ( '2638384' , '2025-25' ) : 0,\n", "            ( '2638381' , '2025-25' ) : 0\n", "\n", "} \n", "\n", "beginning_inventory = {\n", "            ( '2389969' , '2025-15' ) : 40,\n", "            ( '2389980' , '2025-15' ) : 35,\n", "            ( '2389983' , '2025-15' ) : 118,\n", "            ( '2638384' , '2025-15' ) : 61,\n", "            ( '2638381' , '2025-15' ) : 63\n", "\n", "}\n", "\n", "gross_margin_data = {\n", "        ( '2389969' , '2025-15' ) : 75.0615,\n", "        ( '2389969' , '2025-16' ) : 75.0615,\n", "        ( '2389969' , '2025-17' ) : 75.0615,\n", "        ( '2389969' , '2025-18' ) : 75.0615,\n", "        ( '2389969' , '2025-19' ) : 75.0615,\n", "        ( '2389969' , '2025-20' ) : 75.0615,\n", "        ( '2389969' , '2025-21' ) : 75.0615,\n", "        ( '2389969' , '2025-22' ) : 75.0615,\n", "        ( '2389969' , '2025-23' ) : 75.0615,\n", "        ( '2389969' , '2025-24' ) : 75.0615,\n", "        ( '2389969' , '2025-25' ) : 75.0615,\n", "        ( '2389980' , '2025-15' ) : 120.8615,\n", "        ( '2389980' , '2025-16' ) : 120.8615,\n", "        ( '2389980' , '2025-17' ) : 120.8615,\n", "        ( '2389980' , '2025-18' ) : 120.8615,\n", "        ( '2389980' , '2025-19' ) : 120.8615,\n", "        ( '2389980' , '2025-20' ) : 120.8615,\n", "        ( '2389980' , '2025-21' ) : 120.8615,\n", "        ( '2389980' , '2025-22' ) : 120.8615,\n", "        ( '2389980' , '2025-23' ) : 120.8615,\n", "        ( '2389980' , '2025-24' ) : 120.8615,\n", "        ( '2389980' , '2025-25' ) : 120.8615,\n", "        ( '2389983' , '2025-15' ) : 61.9815,\n", "        ( '2389983' , '2025-16' ) : 61.9815,\n", "        ( '2389983' , '2025-17' ) : 61.9815,\n", "        ( '2389983' , '2025-18' ) : 61.9815,\n", "        ( '2389983' , '2025-19' ) : 61.9815,\n", "        ( '2389983' , '2025-20' ) : 61.9815,\n", "        ( '2389983' , '2025-21' ) : 61.9815,\n", "        ( '2389983' , '2025-22' ) : 61.9815,\n", "        ( '2389983' , '2025-23' ) : 61.9815,\n", "        ( '2389983' , '2025-24' ) : 61.9815,\n", "        ( '2389983' , '2025-25' ) : 61.9815,\n", "        ( '2638384' , '2025-15' ) : 176.9815,\n", "        ( '2638384' , '2025-16' ) : 176.9815,\n", "        ( '2638384' , '2025-17' ) : 176.9815,\n", "        ( '2638384' , '2025-18' ) : 176.9815,\n", "        ( '2638384' , '2025-19' ) : 176.9815,\n", "        ( '2638384' , '2025-20' ) : 176.9815,\n", "        ( '2638384' , '2025-21' ) : 176.9815,\n", "        ( '2638384' , '2025-22' ) : 176.9815,\n", "        ( '2638384' , '2025-23' ) : 176.9815,\n", "        ( '2638384' , '2025-24' ) : 176.9815,\n", "        ( '2638384' , '2025-25' ) : 176.9815,\n", "        ( '2638381' , '2025-15' ) : 176.9815,\n", "        ( '2638381' , '2025-16' ) : 176.9815,\n", "        ( '2638381' , '2025-17' ) : 176.9815,\n", "        ( '2638381' , '2025-18' ) : 176.9815,\n", "        ( '2638381' , '2025-19' ) : 176.9815,\n", "        ( '2638381' , '2025-20' ) : 176.9815,\n", "        ( '2638381' , '2025-21' ) : 176.9815,\n", "        ( '2638381' , '2025-22' ) : 176.9815,\n", "        ( '2638381' , '2025-23' ) : 176.9815,\n", "        ( '2638381' , '2025-24' ) : 176.9815,\n", "        ( '2638381' , '2025-25' ) : 176.9815\n", "}\n", "\n", "\n", "model.forecasts = Param( model.forecast_items, model.forecast_period, within = NonNegativeIntegers, initialize = forecast_data)\n", "model.beginning_inv = Param(model.forecast_items, model.start_week, initialize=beginning_inventory)\n", "\n", "model.arrivals = Param( model.forecast_items, model.forecast_period, within = NonNegativeIntegers, initialize=0)\n", "# Variables are only defined for the last 8 periods\n", "model.orders_to_place = Var(model.forecast_items, model.variable_periods, within=NonNegativeIntegers, initialize=0 )\n", "model.num_batches = Var(model.variable_periods, within=NonNegativeIntegers, initialize=0)\n", "\n", "\n", "def calc_ending_inv(m, i, t):\n", "    # For the first period\n", "    if t == m.forecast_period.first():\n", "        return m.beginning_inv[i,t] + m.arrivals[i, t] - m.forecasts[i, t]\n", "    # For periods where variables are not defined (first 4 periods)\n", "    elif t not in m.variable_periods:\n", "        return m.ending_inv[i, m.forecast_period.prev(t)] + m.arrivals[i, t] - m.forecasts[i, t]\n", "    # For periods where variables are defined (last 8 periods)\n", "    else:\n", "        return m.ending_inv[i, m.forecast_period.prev(t)] + m.orders_to_place[i, t] + m.arrivals[i, t] - m.forecasts[i, t]\n", "    \n", "\n", "model.ending_inv = Expression(model.forecast_items, model.forecast_period, rule=calc_ending_inv)\n", "model.storage_volume = Expression(model.forecast_items, model.forecast_period, rule=lambda m, i, t: m.ending_inv[i, t] * 125.23)\n", "model.storage_costs = Expression(model.forecast_items, model.forecast_period, rule=lambda m, i, t: m.storage_volume[i, t] * 0.007)\n", "model.gross_margin = Param(model.forecast_items, model.forecast_period, initialize=gross_margin_data)\n", "model.net_contribution = Expression(model.forecast_items, model.forecast_period, rule=lambda m, i, t: m.gross_margin[i, t] * m.forecasts[i, t] - m.storage_costs[i, t])\n", "model.obj = Objective(rule=lambda m: sum(m.net_contribution[i, t] for i in m.forecast_items for t in m.forecast_period), sense=maximize, name='Maximize_Net_Contribution')\n", "\n", "def order_constraint_rule(m, t):\n", "    return sum(m.orders_to_place[i, t] for i in m.forecast_items)  == model.num_batches[t] * 77\n", "   \n", "# Apply constraint only to the variable periods (last 8 periods)\n", "model.order_constraint = Constraint(model.variable_periods, rule=order_constraint_rule)\n", "\n", "def ending_inv_constraint_rule(m, i, t):\n", "    return m.ending_inv[i, t] >= 1\n", "   \n", "# Apply ending inventory constraint to all periods\n", "model.ending_inv_constraint = Constraint(model.forecast_items, model.variable_periods, rule=ending_inv_constraint_rule)\n", "\n", "solver = SolverFactory('highs')\n", "res=solver.solve(model)\n", "assert_optimal_termination(res)"]}, {"cell_type": "code", "execution_count": 6, "id": "eb6a5144", "metadata": {}, "outputs": [{"data": {"text/plain": ["'TerminationCondition.convergenceCriteriaSatisfied'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["res.solver.termination_message"]}, {"cell_type": "code", "execution_count": 7, "id": "a7014d81", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ending Inventory for Item 2389969 in Period 2025-15: 28\n", "Ending Inventory for Item 2389969 in Period 2025-16: 16\n", "Ending Inventory for Item 2389969 in Period 2025-17: 4\n", "Ending Inventory for Item 2389969 in Period 2025-18: -8\n", "Ending Inventory for Item 2389969 in Period 2025-19: 1.0\n", "Ending Inventory for Item 2389969 in Period 2025-20: 13.0\n", "Ending Inventory for Item 2389969 in Period 2025-21: 1.0\n", "Ending Inventory for Item 2389969 in Period 2025-22: 13.0\n", "Ending Inventory for Item 2389969 in Period 2025-23: 1.0\n", "Ending Inventory for Item 2389969 in Period 2025-24: 13.0\n", "Ending Inventory for Item 2389969 in Period 2025-25: 1.0\n", "Ending Inventory for Item 2389980 in Period 2025-15: 18\n", "Ending Inventory for Item 2389980 in Period 2025-16: 1\n", "Ending Inventory for Item 2389980 in Period 2025-17: -16\n", "Ending Inventory for Item 2389980 in Period 2025-18: -33\n", "Ending Inventory for Item 2389980 in Period 2025-19: 1.0\n", "Ending Inventory for Item 2389980 in Period 2025-20: 26.0\n", "Ending Inventory for Item 2389980 in Period 2025-21: 9.0\n", "Ending Inventory for Item 2389980 in Period 2025-22: 1.0\n", "Ending Inventory for Item 2389980 in Period 2025-23: 34.0\n", "Ending Inventory for Item 2389980 in Period 2025-24: 33.0\n", "Ending Inventory for Item 2389980 in Period 2025-25: 16.0\n", "Ending Inventory for Item 2389983 in Period 2025-15: 88\n", "Ending Inventory for Item 2389983 in Period 2025-16: 58\n", "Ending Inventory for Item 2389983 in Period 2025-17: 28\n", "Ending Inventory for Item 2389983 in Period 2025-18: -2\n", "Ending Inventory for Item 2389983 in Period 2025-19: 50.0\n", "Ending Inventory for Item 2389983 in Period 2025-20: 31.0\n", "Ending Inventory for Item 2389983 in Period 2025-21: 1.0\n", "Ending Inventory for Item 2389983 in Period 2025-22: 4.0\n", "Ending Inventory for Item 2389983 in Period 2025-23: 1.0\n", "Ending Inventory for Item 2389983 in Period 2025-24: 1.0\n", "Ending Inventory for Item 2389983 in Period 2025-25: 48.0\n", "Ending Inventory for Item 2638384 in Period 2025-15: 54\n", "Ending Inventory for Item 2638384 in Period 2025-16: 47\n", "Ending Inventory for Item 2638384 in Period 2025-17: 40\n", "Ending Inventory for Item 2638384 in Period 2025-18: 33\n", "Ending Inventory for Item 2638384 in Period 2025-19: 26.0\n", "Ending Inventory for Item 2638384 in Period 2025-20: 19.0\n", "Ending Inventory for Item 2638384 in Period 2025-21: 12.0\n", "Ending Inventory for Item 2638384 in Period 2025-22: 8.0\n", "Ending Inventory for Item 2638384 in Period 2025-23: 1.0\n", "Ending Inventory for Item 2638384 in Period 2025-24: 1.0\n", "Ending Inventory for Item 2638384 in Period 2025-25: 1.0\n", "Ending Inventory for Item 2638381 in Period 2025-15: 56\n", "Ending Inventory for Item 2638381 in Period 2025-16: 49\n", "Ending Inventory for Item 2638381 in Period 2025-17: 42\n", "Ending Inventory for Item 2638381 in Period 2025-18: 35\n", "Ending Inventory for Item 2638381 in Period 2025-19: 28.0\n", "Ending Inventory for Item 2638381 in Period 2025-20: 21.0\n", "Ending Inventory for Item 2638381 in Period 2025-21: 14.0\n", "Ending Inventory for Item 2638381 in Period 2025-22: 15.0\n", "Ending Inventory for Item 2638381 in Period 2025-23: 8.0\n", "Ending Inventory for Item 2638381 in Period 2025-24: 1.0\n", "Ending Inventory for Item 2638381 in Period 2025-25: 1.0\n"]}], "source": ["for i in model.forecast_items:\n", "    for t in model.forecast_period:\n", "        print(f\"Ending Inventory for Item {i} in Period {t}: {value(model.ending_inv[i, t])}\")"]}, {"cell_type": "code", "execution_count": 9, "id": "58d9ec01", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2389969 2025-15 12 28 N/A (not in variable periods)\n", "2389969 2025-16 12 16 N/A (not in variable periods)\n", "2389969 2025-17 12 4 N/A (not in variable periods)\n", "2389969 2025-18 12 -8 N/A (not in variable periods)\n", "2389969 2025-19 12 1.0 21.0\n", "2389969 2025-20 12 13.0 24.0\n", "2389969 2025-21 12 1.0 0.0\n", "2389969 2025-22 12 13.0 24.0\n", "2389969 2025-23 12 1.0 0.0\n", "2389969 2025-24 12 13.0 24.0\n", "2389969 2025-25 12 1.0 0.0\n", "2389980 2025-15 17 18 N/A (not in variable periods)\n", "2389980 2025-16 17 1 N/A (not in variable periods)\n", "2389980 2025-17 17 -16 N/A (not in variable periods)\n", "2389980 2025-18 17 -33 N/A (not in variable periods)\n", "2389980 2025-19 17 1.0 51.0\n", "2389980 2025-20 17 26.0 42.0\n", "2389980 2025-21 17 9.0 0.0\n", "2389980 2025-22 17 1.0 9.0\n", "2389980 2025-23 17 34.0 50.0\n", "2389980 2025-24 17 33.0 16.0\n", "2389980 2025-25 17 16.0 0.0\n", "2389983 2025-15 30 88 N/A (not in variable periods)\n", "2389983 2025-16 30 58 N/A (not in variable periods)\n", "2389983 2025-17 30 28 N/A (not in variable periods)\n", "2389983 2025-18 30 -2 N/A (not in variable periods)\n", "2389983 2025-19 30 50.0 82.0\n", "2389983 2025-20 30 31.0 11.0\n", "2389983 2025-21 30 1.0 0.0\n", "2389983 2025-22 30 4.0 33.0\n", "2389983 2025-23 30 1.0 27.0\n", "2389983 2025-24 30 1.0 30.0\n", "2389983 2025-25 30 48.0 77.0\n", "2638384 2025-15 7 54 N/A (not in variable periods)\n", "2638384 2025-16 7 47 N/A (not in variable periods)\n", "2638384 2025-17 7 40 N/A (not in variable periods)\n", "2638384 2025-18 7 33 N/A (not in variable periods)\n", "2638384 2025-19 7 26.0 0.0\n", "2638384 2025-20 7 19.0 0.0\n", "2638384 2025-21 7 12.0 0.0\n", "2638384 2025-22 7 8.0 3.0\n", "2638384 2025-23 7 1.0 0.0\n", "2638384 2025-24 7 1.0 7.0\n", "2638384 2025-25 0 1.0 0.0\n", "2638381 2025-15 7 56 N/A (not in variable periods)\n", "2638381 2025-16 7 49 N/A (not in variable periods)\n", "2638381 2025-17 7 42 N/A (not in variable periods)\n", "2638381 2025-18 7 35 N/A (not in variable periods)\n", "2638381 2025-19 7 28.0 0.0\n", "2638381 2025-20 7 21.0 0.0\n", "2638381 2025-21 7 14.0 0.0\n", "2638381 2025-22 7 15.0 8.0\n", "2638381 2025-23 7 8.0 0.0\n", "2638381 2025-24 7 1.0 0.0\n", "2638381 2025-25 0 1.0 0.0\n", "orders_to_place : Size=35, Index=forecast_items*variable_periods\n", "    Key                    : Lower : Value : Upper : Fixed : Stale : Domain\n", "    ('2389969', '2025-19') :     0 :  21.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389969', '2025-20') :     0 :  24.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389969', '2025-21') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389969', '2025-22') :     0 :  24.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389969', '2025-23') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389969', '2025-24') :     0 :  24.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389969', '2025-25') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389980', '2025-19') :     0 :  51.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389980', '2025-20') :     0 :  42.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389980', '2025-21') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389980', '2025-22') :     0 :   9.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389980', '2025-23') :     0 :  50.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389980', '2025-24') :     0 :  16.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389980', '2025-25') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389983', '2025-19') :     0 :  82.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389983', '2025-20') :     0 :  11.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389983', '2025-21') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389983', '2025-22') :     0 :  33.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389983', '2025-23') :     0 :  27.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389983', '2025-24') :     0 :  30.0 :  None : False : False : NonNegativeIntegers\n", "    ('2389983', '2025-25') :     0 :  77.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638381', '2025-19') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638381', '2025-20') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638381', '2025-21') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638381', '2025-22') :     0 :   8.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638381', '2025-23') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638381', '2025-24') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638381', '2025-25') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638384', '2025-19') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638384', '2025-20') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638384', '2025-21') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638384', '2025-22') :     0 :   3.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638384', '2025-23') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638384', '2025-24') :     0 :   7.0 :  None : False : False : NonNegativeIntegers\n", "    ('2638384', '2025-25') :     0 :   0.0 :  None : False : False : NonNegativeIntegers\n", "ending_inv : Size=55\n", "    Key                    : Value\n", "    ('2389969', '2025-15') :    28\n", "    ('2389969', '2025-16') :    16\n", "    ('2389969', '2025-17') :     4\n", "    ('2389969', '2025-18') :    -8\n", "    ('2389969', '2025-19') :   1.0\n", "    ('2389969', '2025-20') :  13.0\n", "    ('2389969', '2025-21') :   1.0\n", "    ('2389969', '2025-22') :  13.0\n", "    ('2389969', '2025-23') :   1.0\n", "    ('2389969', '2025-24') :  13.0\n", "    ('2389969', '2025-25') :   1.0\n", "    ('2389980', '2025-15') :    18\n", "    ('2389980', '2025-16') :     1\n", "    ('2389980', '2025-17') :   -16\n", "    ('2389980', '2025-18') :   -33\n", "    ('2389980', '2025-19') :   1.0\n", "    ('2389980', '2025-20') :  26.0\n", "    ('2389980', '2025-21') :   9.0\n", "    ('2389980', '2025-22') :   1.0\n", "    ('2389980', '2025-23') :  34.0\n", "    ('2389980', '2025-24') :  33.0\n", "    ('2389980', '2025-25') :  16.0\n", "    ('2389983', '2025-15') :    88\n", "    ('2389983', '2025-16') :    58\n", "    ('2389983', '2025-17') :    28\n", "    ('2389983', '2025-18') :    -2\n", "    ('2389983', '2025-19') :  50.0\n", "    ('2389983', '2025-20') :  31.0\n", "    ('2389983', '2025-21') :   1.0\n", "    ('2389983', '2025-22') :   4.0\n", "    ('2389983', '2025-23') :   1.0\n", "    ('2389983', '2025-24') :   1.0\n", "    ('2389983', '2025-25') :  48.0\n", "    ('2638381', '2025-15') :    56\n", "    ('2638381', '2025-16') :    49\n", "    ('2638381', '2025-17') :    42\n", "    ('2638381', '2025-18') :    35\n", "    ('2638381', '2025-19') :  28.0\n", "    ('2638381', '2025-20') :  21.0\n", "    ('2638381', '2025-21') :  14.0\n", "    ('2638381', '2025-22') :  15.0\n", "    ('2638381', '2025-23') :   8.0\n", "    ('2638381', '2025-24') :   1.0\n", "    ('2638381', '2025-25') :   1.0\n", "    ('2638384', '2025-15') :    54\n", "    ('2638384', '2025-16') :    47\n", "    ('2638384', '2025-17') :    40\n", "    ('2638384', '2025-18') :    33\n", "    ('2638384', '2025-19') :  26.0\n", "    ('2638384', '2025-20') :  19.0\n", "    ('2638384', '2025-21') :  12.0\n", "    ('2638384', '2025-22') :   8.0\n", "    ('2638384', '2025-23') :   1.0\n", "    ('2638384', '2025-24') :   1.0\n", "    ('2638384', '2025-25') :   1.0\n"]}], "source": ["for i in model.forecast_items :\n", "     for t in model.forecast_period:\n", "          if t in model.variable_periods:\n", "               print( i, t, value( model.forecasts[i,t]), value(model.ending_inv[i, t]), value( model.orders_to_place[i,t]) )\n", "          else:\n", "               print( i, t, value( model.forecasts[i,t]), value(model.ending_inv[i, t]), \"N/A (not in variable periods)\" )\n", "model.orders_to_place.display()\n", "model.ending_inv.display()"]}, {"cell_type": "code", "execution_count": null, "id": "185b06f1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>2025-15</th>\n", "      <th>2025-16</th>\n", "      <th>2025-17</th>\n", "      <th>2025-18</th>\n", "      <th>2025-19</th>\n", "      <th>2025-20</th>\n", "      <th>2025-21</th>\n", "      <th>2025-22</th>\n", "      <th>2025-23</th>\n", "      <th>2025-24</th>\n", "      <th>2025-25</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2389969</th>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2389980</th>\n", "      <td>17</td>\n", "      <td>17</td>\n", "      <td>17</td>\n", "      <td>17</td>\n", "      <td>17</td>\n", "      <td>17</td>\n", "      <td>17</td>\n", "      <td>17</td>\n", "      <td>17</td>\n", "      <td>17</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2389983</th>\n", "      <td>30</td>\n", "      <td>30</td>\n", "      <td>30</td>\n", "      <td>30</td>\n", "      <td>30</td>\n", "      <td>30</td>\n", "      <td>30</td>\n", "      <td>30</td>\n", "      <td>30</td>\n", "      <td>30</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2638384</th>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2638381</th>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         2025-15  2025-16  2025-17  2025-18  2025-19  2025-20  2025-21  \\\n", "2389969       12       12       12       12       12       12       12   \n", "2389980       17       17       17       17       17       17       17   \n", "2389983       30       30       30       30       30       30       30   \n", "2638384        7        7        7        7        7        7        7   \n", "2638381        7        7        7        7        7        7        7   \n", "\n", "         2025-22  2025-23  2025-24  2025-25  \n", "2389969       12       12       12       12  \n", "2389980       17       17       17       17  \n", "2389983       30       30       30       30  \n", "2638384        7        7        7        0  \n", "2638381        7        7        7        0  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["forecast_data = { t: {i: model.forecasts[i, t] for i in model.forecast_items} for t in model.forecast_period}\n", "arrivals_data = {t: {i: model.arrivals[i, t] for i in  model.forecast_items} for t in model.forecast_period}\n", "orders_to_place_data = {t: {i: value(model.orders_to_place[i, t]) for i in model.forecast_items} for t in model.variable_periods}\n", "ending_inv_data = {t: {i: value(model.ending_inv[i, t]) for i in model.forecast_items} for t in model.forecast_period}\n", "\n"]}, {"cell_type": "code", "execution_count": 26, "id": "bedff3dd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>week</th>\n", "      <th>2025-15</th>\n", "      <th>2025-16</th>\n", "      <th>2025-17</th>\n", "      <th>2025-18</th>\n", "      <th>2025-19</th>\n", "      <th>2025-20</th>\n", "      <th>2025-21</th>\n", "      <th>2025-22</th>\n", "      <th>2025-23</th>\n", "      <th>2025-24</th>\n", "      <th>2025-25</th>\n", "    </tr>\n", "    <tr>\n", "      <th>type</th>\n", "      <th>itemid</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">forecast</th>\n", "      <th>2389969</th>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2389980</th>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2389983</th>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2638384</th>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2638381</th>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">arrivals</th>\n", "      <th>2389969</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2389980</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2389983</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2638384</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2638381</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">orders_to_place</th>\n", "      <th>2389969</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>21.0</td>\n", "      <td>24.0</td>\n", "      <td>0.0</td>\n", "      <td>24.0</td>\n", "      <td>0.0</td>\n", "      <td>24.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2389980</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>51.0</td>\n", "      <td>42.0</td>\n", "      <td>0.0</td>\n", "      <td>9.0</td>\n", "      <td>50.0</td>\n", "      <td>16.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2389983</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>82.0</td>\n", "      <td>11.0</td>\n", "      <td>0.0</td>\n", "      <td>33.0</td>\n", "      <td>27.0</td>\n", "      <td>30.0</td>\n", "      <td>77.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2638384</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2638381</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>8.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">ending_inv</th>\n", "      <th>2389969</th>\n", "      <td>28.0</td>\n", "      <td>16.0</td>\n", "      <td>4.0</td>\n", "      <td>-8.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2389980</th>\n", "      <td>18.0</td>\n", "      <td>1.0</td>\n", "      <td>-16.0</td>\n", "      <td>-33.0</td>\n", "      <td>1.0</td>\n", "      <td>26.0</td>\n", "      <td>9.0</td>\n", "      <td>1.0</td>\n", "      <td>34.0</td>\n", "      <td>33.0</td>\n", "      <td>16.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2389983</th>\n", "      <td>88.0</td>\n", "      <td>58.0</td>\n", "      <td>28.0</td>\n", "      <td>-2.0</td>\n", "      <td>50.0</td>\n", "      <td>31.0</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>48.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2638384</th>\n", "      <td>54.0</td>\n", "      <td>47.0</td>\n", "      <td>40.0</td>\n", "      <td>33.0</td>\n", "      <td>26.0</td>\n", "      <td>19.0</td>\n", "      <td>12.0</td>\n", "      <td>8.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2638381</th>\n", "      <td>56.0</td>\n", "      <td>49.0</td>\n", "      <td>42.0</td>\n", "      <td>35.0</td>\n", "      <td>28.0</td>\n", "      <td>21.0</td>\n", "      <td>14.0</td>\n", "      <td>15.0</td>\n", "      <td>8.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["week                     2025-15  2025-16  2025-17  2025-18  2025-19  2025-20  \\\n", "type            itemid                                                          \n", "forecast        2389969     12.0     12.0     12.0     12.0     12.0     12.0   \n", "                2389980     17.0     17.0     17.0     17.0     17.0     17.0   \n", "                2389983     30.0     30.0     30.0     30.0     30.0     30.0   \n", "                2638384      7.0      7.0      7.0      7.0      7.0      7.0   \n", "                2638381      7.0      7.0      7.0      7.0      7.0      7.0   \n", "arrivals        2389969      0.0      0.0      0.0      0.0      0.0      0.0   \n", "                2389980      0.0      0.0      0.0      0.0      0.0      0.0   \n", "                2389983      0.0      0.0      0.0      0.0      0.0      0.0   \n", "                2638384      0.0      0.0      0.0      0.0      0.0      0.0   \n", "                2638381      0.0      0.0      0.0      0.0      0.0      0.0   \n", "orders_to_place 2389969      NaN      NaN      NaN      NaN     21.0     24.0   \n", "                2389980      NaN      NaN      NaN      NaN     51.0     42.0   \n", "                2389983      NaN      NaN      NaN      NaN     82.0     11.0   \n", "                2638384      NaN      NaN      NaN      NaN      0.0      0.0   \n", "                2638381      NaN      NaN      NaN      NaN      0.0      0.0   \n", "ending_inv      2389969     28.0     16.0      4.0     -8.0      1.0     13.0   \n", "                2389980     18.0      1.0    -16.0    -33.0      1.0     26.0   \n", "                2389983     88.0     58.0     28.0     -2.0     50.0     31.0   \n", "                2638384     54.0     47.0     40.0     33.0     26.0     19.0   \n", "                2638381     56.0     49.0     42.0     35.0     28.0     21.0   \n", "\n", "week                     2025-21  2025-22  2025-23  2025-24  2025-25  \n", "type            itemid                                                \n", "forecast        2389969     12.0     12.0     12.0     12.0     12.0  \n", "                2389980     17.0     17.0     17.0     17.0     17.0  \n", "                2389983     30.0     30.0     30.0     30.0     30.0  \n", "                2638384      7.0      7.0      7.0      7.0      0.0  \n", "                2638381      7.0      7.0      7.0      7.0      0.0  \n", "arrivals        2389969      0.0      0.0      0.0      0.0      0.0  \n", "                2389980      0.0      0.0      0.0      0.0      0.0  \n", "                2389983      0.0      0.0      0.0      0.0      0.0  \n", "                2638384      0.0      0.0      0.0      0.0      0.0  \n", "                2638381      0.0      0.0      0.0      0.0      0.0  \n", "orders_to_place 2389969      0.0     24.0      0.0     24.0      0.0  \n", "                2389980      0.0      9.0     50.0     16.0      0.0  \n", "                2389983      0.0     33.0     27.0     30.0     77.0  \n", "                2638384      0.0      3.0      0.0      7.0      0.0  \n", "                2638381      0.0      8.0      0.0      0.0      0.0  \n", "ending_inv      2389969      1.0     13.0      1.0     13.0      1.0  \n", "                2389980      9.0      1.0     34.0     33.0     16.0  \n", "                2389983      1.0      4.0      1.0      1.0     48.0  \n", "                2638384     12.0      8.0      1.0      1.0      1.0  \n", "                2638381     14.0     15.0      8.0      1.0      1.0  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df_forecast = pd.DataFrame(forecast_data).rename_axis(index='itemid', columns='week').assign(type='forecast').set_index('type', append=True)\n", "df_arrivals = pd.DataFrame(arrivals_data).rename_axis(index='itemid', columns='week').assign(type='arrivals').set_index('type', append=True)\n", "df_orders_to_place = pd.DataFrame(orders_to_place_data).rename_axis(index='itemid', columns='week').assign(type='orders_to_place').set_index('type', append=True)\n", "df_ending_inv = pd.DataFrame(ending_inv_data).rename_axis(index='itemid', columns='week').assign(type='ending_inv').set_index('type', append=True)\n", "consolidated_report_df = pd.concat([df_forecast, df_arrivals, df_orders_to_place, df_ending_inv], axis=0)\n", "consolidated_report_df.swaplevel(0,1)"]}], "metadata": {"kernelspec": {"display_name": "ml", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}