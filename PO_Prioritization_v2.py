# %%
import pandas as pd
import numpy as np
import pyodbc
import datetime
from math import ceil,floor
import xlwings as xl
from tkinter import messagebox
from tkinter import *
import tkinter as tk
from tkinter import ttk
from tkcalendar import Calendar
from IPython.display import clear_output
from datetime import date 

import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)


# %%
import openpyxl
Planner = pd.read_excel('Factory Data.xlsx')
Planner1 = Planner[['itemid','factory','Planner']]


# %%
def get_user():
    def submit():
        global user
        user = auditor_combobox.get()
        root.destroy()
        
    # Create the main window
    root = tk.Tk()
    root.title("Planner Selection")
    # root.geometry("200x150")  # Set the window size
 
    # Set background color
    root.configure(bg='#F0F0F0')
 
    # Create a frame for better layout management
    frame = tk.Frame(root, bg='#F0F0F0')
    frame.pack(padx=20, pady=20, fill='both', expand=True)
 
    # Auditor label
    tk.Label(frame, text="Planner Name:", font=('Arial', 12, 'bold'), bg='#F0F0F0').pack(pady=(0, 10))
 
    # Auditor combobox
    auditors = Planner1['Planner'].unique().tolist()
    auditor_combobox = ttk.Combobox(frame, values=auditors, state="readonly", width=20)
    auditor_combobox.pack(pady=(0, 10))  # Add padding below the combobox
    auditor_combobox.set(auditors[0])  # Set default value
 
    # Add a button for action (optional)
    tk.Button(frame, text="Submit", command=submit).pack(pady=(10, 0))
 
    # Center the window on the screen
    root.eval('tk::PlaceWindow . center')
    root.mainloop()
     
get_user()

# %%
Planner_Name = user
Planner_item = Planner1[Planner1['Planner']==Planner_Name]

# %%
Planner_item

# %%
uid = 'nh_pos'
pwdlist = [80, 111, 64, 109, 97, 99, 114, 111]
pwd = ''.join([chr(i) for i in pwdlist])
server = 'sql' if uid=='nh_pos' else 'sql'

def sqlconnect(uid, pwd):
    conn = pyodbc.connect(
        "Driver={SQL Server Native Client 11.0};"
        f"Server={server}.ojcommerce.com;"
        "Database=ojcommerce_reader;"
        "UID="+uid+";"
        "PWD="+pwd+";"
        "MultipleActiveResultSets=False;"
    )
    return conn


sql = sqlconnect(uid, pwd)
db = sql.cursor()


query = f'''

set nocount on;
IF OBJECT_ID('tempdb..#assemblies') IS NOT NULL DROP TABLE #assemblies
IF OBJECT_ID('tempdb..#originalids') IS NOT NULL DROP TABLE #originalids
IF OBJECT_ID('tempdb..#noassembly') IS NOT NULL DROP TABLE #noassembly
IF OBJECT_ID('tempdb..#singleassemblies') IS NOT NULL DROP TABLE #singleassemblies
IF OBJECT_ID('tempdb..#itemtype') IS NOT NULL DROP TABLE #itemtype
 
create table #assemblies (created datetime,id int,assemblyids varchar(250))
create table #originalids (created datetime,id int,assemblyids varchar(250),rn int)
create table  #singleassemblies (itemid int, assemblyid int)
 
insert into #assemblies
select distinct pd.created,pd.id,coalesce(String_agg(cast(pa.assemblyid as varchar(10))+','+cast(pa.qty as varchar(10)),',') WITHIN GROUP (ORDER BY assemblyid ASC),cast(pd.id as varchar(10))+',1') as assemblyids
from product_details pd WITH (NOLOCK) 
join product p WITH (NOLOCK) on p.id = pd.pid
left join product_assemblies pa WITH (NOLOCK) on pa.itemid = pd.id
where
p.manufacturer_id = 3567
or pd.id in (select pa2.itemid from product_assemblies pa2 with (nolock) join product_details pd2 with (nolock) on pd2.id = pa2.assemblyid join product p2 with (nolock) on p2.id = pd2.pid where p2.manufacturer_id = 3567)
group by pd.id,pd.created,p.name,pd.price,pd.pid
 
 
create table #noassembly (itemid int)
insert into #noassembly
select
a.id as itemid
from
#assemblies a
left join product_assemblies pa with (nolock) on a.id = pa.itemid
where pa.itemid is null
 
 
update #assemblies
set created = 0
where id in (select n.itemid from #noassembly n)
 
 
insert into #originalids
select
asm.*,
ROW_NUMBER() OVER (PARTITION BY asm.assemblyids ORDER BY asm.created ASC) AS rn
from #assemblies asm
 
 
insert into #singleassemblies
select
pacount.itemid,
pacount.assemblyid
from
(select
pa.itemid,
pa.assemblyid,
(select count(pa2.itemid) from product_assemblies pa2 with (nolock) where pa2.itemid = pa.itemid) as counts,
(select sum(pa2.qty) from product_assemblies pa2 with (nolock) where pa2.itemid = pa.itemid) as qty
from product_assemblies pa with (nolock)
join product_details pd with (nolock) on pd.id = pa.assemblyid
join product p with (nolock) on pd.pid = p.id
where p.manufacturer_id = 3567) pacount
where pacount.counts = 1
and pacount.qty = 1
 
create table #itemtype (Itemid int,Itemtype varchar(15),OriginalID int)
insert into #itemtype
select
n.itemid,
'Original' as Itemtype,
a.id as OriginalID
from
#noassembly n
join #assemblies a on a.id = n.itemid
join product_details pd with (nolock) on pd.id = n.itemid
 
insert into #itemtype
select
s.itemid,
'Whitelabel' as Itemtype,
a.id as OriginalID
from
#singleassemblies s
join #assemblies a on a.id = s.assemblyid
join product_details pd with (nolock) on pd.id = s.itemid
 
 
insert into #itemtype
select
pas.id as Itemid,
case when pas.id = original.id
then 'Original'
else 'Whitelabel'
end as Itemtype,
original.id as OriginalID
from
#assemblies pas
join #originalids original on original.assemblyids = pas.assemblyids and original.rn = 1
join product_details pd with (nolock) on original.id = pd.id
where
pas.id not in (select s.itemid from #singleassemblies s)
and pas.id not in (select n.itemid from #noassembly n)
order by pas.id
 
select i.*,
coalesce(pa.assemblyid,i.originalid) as parts,
coalesce(pa.qty,1) as qty
from #itemtype i
left join product_assemblies pa with (nolock) on pa.itemid = i.originalid
'''

assemblytable = pd.read_sql(query, sql)
# y = db.fetchall()
db.close()
sql.close()  

# %%
assemblytable1 = assemblytable[assemblytable['Itemtype']=='Original']

# %%
assemblytable1 = assemblytable1[['OriginalID','parts']]
assemblytable1

# %%
assemblytable1.drop_duplicates()

# %%
OrginalID_parts = pd.merge(Planner_item,assemblytable1,'left',left_on='itemid',right_on='parts')


# %%
OrginalID_parts = OrginalID_parts[['OriginalID']].drop_duplicates()

# %%
OrginalID_parts = OrginalID_parts.dropna(subset=['OriginalID'])


# %%
OrginalID_parts['OriginalID']= OrginalID_parts['OriginalID'].astype(int)

# %%
Finalized_items_list = ",".join([f"'{itemid}'" for itemid in OrginalID_parts['OriginalID']])


# %%

orange = (255, 165, 0)
blue = (153, 204, 255)
pink = (255, 204, 255)
purple = (204, 102, 255)
cream = (102, 255, 102)
stored_data = {}

today = datetime.datetime.now().date().strftime('%d-%m-%Y')
stored_data['date'] = today


# %%
new_po_added=False
wb = xl.Book('PO_Prioritization.xlsm')
dt = wb.sheets('Data')
sh = wb.sheets('PO_Prioritization')


# %%
new_po_added=False
wb = xl.Book('PO_Prioritization.xlsm')
dt = wb.sheets('Data')
sh = wb.sheets('PO_Prioritization')

# %%
uid = 'premj'
pwd = 'Premantony@13'

def sqlconnect(uid, pwd):
    conn = pyodbc.connect(
        "Driver={SQL Server Native Client 11.0};"
        "Server=************;"
        "Database=Naomihome;"
        "UID="+uid+";"
        "PWD="+pwd+";"
        "MultipleActiveResultSets=False;"
    )
    return conn


# %%
sql = sqlconnect(uid, pwd)
db = sql.cursor()
query = f'''SELECT nf.itemId, nf.Week, nf.value
FROM NH_forecastdata nf WITH (NOLOCK)
WHERE nf.forecastdate = (
    SELECT MAX(forecastdate)
    FROM NH_forecastdata
    WHERE Type = 'Manual'
    AND nf.itemId IN ({Finalized_items_list})
)
'''
result_batch = pd.read_sql(query, sql)
# y = db.fetchall()
db.close()
sql.close()  

# %%
len(result_batch['itemId'].unique())

# %%
current_date = datetime.date.today()
current_week = current_date.isocalendar()[1]
current_year = current_date.year
current_week_str = f"{current_week:02d}"
current_week_year = f"{current_year}-{current_week_str}"
current_week_year
result_batch1 = result_batch[result_batch['Week'] >= current_week_year]


# %%
uid = 'nh_pos'
pwdlist = [80, 111, 64, 109, 97, 99, 114, 111]
pwd = ''.join([chr(i) for i in pwdlist])
server = 'sql' if uid=='nh_pos' else 'sql'

def sqlconnect(uid, pwd):
    conn = pyodbc.connect(
        "Driver={SQL Server Native Client 11.0};"
        f"Server={server}.ojcommerce.com;"
        "Database=ojcommerce_reader;"
        "UID="+uid+";"
        "PWD="+pwd+";"
        "MultipleActiveResultSets=False;"
    )
    return conn

# %%
sql = sqlconnect(uid, pwd)
db = sql.cursor()

# %%

query = f'''
select
pd.id,
p.name,
(select string_agg(value,',')
  WITHIN GROUP (ORDER BY type_id ASC)
  from var_values with (nolock)
  where id in (
  select distinct varid
   from var_items with (nolock)
   where itemid = pd.id) ) as Variation
from
product_Details pd
join product p  on p.id = pd.pid
where p.manufacturer_id in (3567,13741)
and pd.id IN ({Finalized_items_list})
'''
itemname = pd.read_sql(query, sql)
# y = db.fetchall()
db.close()
sql.close()  

# %%
itemname['Name'] = itemname['name'] + ' - ' + itemname['Variation']

# %%
df = pd.merge(result_batch1, itemname[['id', 'Name']] ,how='left', left_on='itemId', right_on='id')

# %%
df.pop('id')

# %%

df_wide = df.pivot_table(index=['itemId', 'Name'], columns='Week', values='value', aggfunc='first').reset_index()
df_wide.columns = ['_'.join(map(str, col)) if isinstance(col, tuple) else col for col in df_wide.columns]


# %%
current_week_1 = current_date.isocalendar()[1] -1
current_year_1 = current_date.year +1
current_week_1_str = f"{current_week_1:02d}"
current_week_year_1 = f"{current_year_1}-{current_week_1_str}"
current_week_year_1
df_wide[current_week_year_1] = df_wide[df_wide.columns[-1]]


# %%
sheet = wb.sheets['Sales_FC_volume']  # Change to your target sheet name

sheet.range('A1').expand().clear_contents()

sheet.range('A1').value = df_wide.values.tolist()  

sheet.range('A1').value = [df_wide.columns.tolist()] + df_wide.values.tolist()

wb.save()

# %%
def sqlconnect(uid, pwd):
    conn = pyodbc.connect(
        "Driver={SQL Server Native Client 11.0};"
        f"Server={server}.ojcommerce.com;"
        "Database=ojcommerce_reader;"
        "UID="+uid+";"
        "PWD="+pwd+";"
        "MultipleActiveResultSets=False;"
    )
    return conn

sql = sqlconnect(uid, pwd)
db = sql.cursor()



# %%
def wnum(date):
    date - datetime.timedelta(days=(wday(date)-1))
    y = date.isocalendar().year
    w = date.isocalendar().week
    if date.weekday() == 6:
        w = w + 1

    return f"{y}-{w:02}"

def wday(date):
    return date.isoweekday()+1

def second(x):
    return x[1]

# %%
storage_est = wb.sheets('Config').range('storage_est').value
margin_est = wb.sheets('Config').range('margin_est').value
preweek = max(wb.sheets('Config').range('presell').value,1)
presell = min(wb.sheets('Config').range('preweek').value,1)
dfc = wb.sheets('Sales_FC_Volume').used_range.options(pd.DataFrame, index=False).value
dfc.columns.values[0] = 'itemid'
dfc['itemid'] = dfc['itemid'].astype(int)
curweek = dfc.columns[2]

# %%
wdate = datetime.date(int(curweek[:4]),1,1) + datetime.timedelta(7*(int(curweek[-2:])-1)) 
wcols = list(dfc.columns[2:-1])
dt_to_wk = dict(zip([wdate + datetime.timedelta(7*i) for i in range(len(wcols))],wcols))
wk_to_dt = {v:k for k,v in dt_to_wk.items()}
is_refresh=False



# %%
def update_header():
    h_offset_dict = {'podf':6,'rsu':1,'hrsu':1,'srsu':1,'hav':1,'sav':1,'hav':1,'cl_inv':1,'woi':1,'adfc1':1}
    tbllist = ['podf','rsu','hrsu','srsu','hav','sav','cl_inv','invdf','woi','adfc1']
    for sht in wb.sheets:
        for tbl in sht.tables:
            if tbl.name in tbllist:
                try:
                    sht.range(tbl.name+'[#Headers]')[0,h_offset_dict[tbl.name]].value = wcols
                except:
                    ''
update_header()

# %%

def margin_data():
    global margin

    sql = sqlconnect(uid, pwd)
    db = sql.cursor()

    a1 = db.execute(f"""select pd.id, case when pd.price=0 then 0 else ((pd.price * 0.85 - coalesce(pd.calculated_shipping_cost,0) - isnull(coalesce(pd.standard_cost,pd.cost),0) )) end as margin, p.name, isnull(coalesce(pd.standard_cost,pd.cost),0) as cost,pd.price 
                            from product_details pd 
                            left join product p  on p.id = pd.pid 
                            where p.manufacturer_id in (3567,13741) and pd.id in ({Finalized_items_list})""")
    y = db.fetchall()
    for j in range(len(y)):
        y[j] = list(y[j])
    db.close()
    sql.close()

    margin = pd.DataFrame(columns = ['id','margin','name','cost','price'])
    margin[['id','margin','name','cost','price']] = y

    margin['margin'] = margin['margin'].apply(lambda x:max(x,0))
    margin['margin'] = margin['margin'].astype(float)
    margin['cost'] = margin['cost'].astype(float)
    margin['price'] = margin['price'].astype(float)
    dt.tables('margin').update(margin,index=False)

# %%

def assmbly_data():
    global adf, assembly_collist

    sql = sqlconnect(uid, pwd)
    db = sql.cursor()

    a1 = db.execute("""with ids as(
                        select id from product_details where pid in (select id from product where manufacturer_id=3567)) 
                    select a.itemid, pd.mpn, p.name, a.qty, a.assemblyid 
                    from product_assemblies a 
                    join product_details pd on pd.id = a.itemid 
                    join product p on p.id = pd.pid 
                    where a.itemid in (select * from ids)""")
    y = db.fetchall()
    for j in range(len(y)):
        y[j] = list(y[j])
    db.close()
    sql.close()

    assembly_collist = ['itemid', 'mpn', 'name', 'qty', 'assemblyid']
    adf = pd.DataFrame(columns = assembly_collist)
    adf[assembly_collist] = y
    del y
    adf['assemblyid'] = adf['assemblyid'].astype(int)
    adf['itemid'] = adf['itemid'].astype(int)
    for x in range(4):
        for aid in set(adf.assemblyid.tolist()).intersection(adf.itemid.tolist()):
            for iid in adf[adf['assemblyid']==aid]['itemid'].tolist():
                ndf = adf[adf['itemid']==aid].copy()
                ndf['itemid'] = iid
                adf = pd.concat([adf,ndf], ignore_index=True)
                adf.drop(adf[(adf['assemblyid']==aid) & (adf['itemid']==iid)].index, inplace=True)
                # print(aid, iid)
    adf.reset_index(drop=True, inplace=True)


# %%

def sort_dfc():
    profitlist = []
    for i in dfc.itemid.tolist():
        profitlist.append(round(margin[margin['id']==i]['margin'].tolist()[0] * dfc.loc[dfc['itemid']==i,wcols].values.sum(),2))
    dfc['profit'] = profitlist
    dfc['profit'] = dfc['profit'].astype(float)
    dfc.sort_values('profit',ascending=False,ignore_index=True,inplace=True)
    dfc.drop('profit',axis=1,inplace=True)
def fc_assembly_split():
    global adfc1, iidlist
    adfccols = ['itemid', 'assemblyid', 'Name']
    adfccols.extend(wcols)
    adfccols.append('Annual Demand')
    #Assembly_Level_Demand_Forecast
    adfc = pd.DataFrame(columns=adfccols)
    for rn in range(len(dfc)):
        i = dfc.itemid.tolist()[rn]
        iname = dfc.loc[rn,'Name']
        if i in adf.itemid.tolist():
            for a in adf[adf['itemid']==i]['assemblyid']:
                q = adf[((adf['itemid']==i)&(adf['assemblyid']==a))]['qty'].tolist()[0]
                temp = [i, a, iname]
                temp.extend(list(dfc.iloc[rn,2:].values * q))
                adfc.loc[len(adfc)] = temp
        else:
            temp = [i, i, iname]
            temp.extend(list(dfc.iloc[rn,2:].values))
            adfc.loc[len(adfc)] = temp
    adfc1 = adfc.drop(['itemid', 'Name'],axis=1).groupby('assemblyid').aggregate('sum')
    adfc1['itemid'] = adfc1.index
    adfc1.reset_index(drop=True, inplace=True)
    adfc1.insert(0,'itemid',adfc1.pop('itemid'))
    adfc1['iidcat'] = pd.Categorical(adfc1['itemid'],adfc['assemblyid'].drop_duplicates().tolist())
    adfc1.sort_values('iidcat',inplace=True,ignore_index=True)
    iidlist = adfc1.itemid.tolist()
    adfc1[wcols[0]] = np.ceil(adfc1[wcols[0]].values *((7-wday(datetime.date.today())-1)/7))
    
    adfc1[wcols[0]] = adfc1[wcols[0]] + adfc1.itemid.apply(lambda x: 0 if x not in presell_dict.keys() else presell_dict[x])
    wb.sheets('Sales_FC_Assembly').tables('adfc1').update(adfc1,index=False)

def country_data():
    global country
    sql = sqlconnect(uid, pwd)
    db = sql.cursor()

    query = '''select f.name 'factory',c.name 'country' from factory f
    join factoryports fp on fp.factoryid = f.id
    join ports p on p.id = fp.portid
    join country c on c.id = p.CountryId'''
    if uid=='Pirrabhu':
        query = '''select f.name 'factory',c.name 'country' from factory f
            join factoryports fp on fp.factoryid = f.id
            join ports p on p.id = fp.portid
            join country c on c.id = p.CountryId'''
    a1 = db.execute(query)
    y = db.fetchall()
    for j in range(len(y)):
        y[j] = list(y[j])
    db.close()
    sql.close()

    country = pd.DataFrame(y,columns=['factory','country'])

    wb.sheets('Data').tables('country').update(country,index=False)


# %%
#country_data()

# %%

def item_list_data():
    global item_list
    sql = sqlconnect(uid, pwd)
    db = sql.cursor()
    a1 = db.execute(f'''select pd.id,p.name, p.name + ' : ' + isnull((select top 1 stuff(( select ',' + u.feature 
                            from (select itemid, value as feature 
                                        from var_items vi 
                                        join var_values vv on vv.id = vi.varid 
                                        join var_types vt on vt.id = vv.type_id where vi.itemid = pd.id) u 
                                        where u.itemid = f.itemid order by u.itemid for xml path('') ),1,1,'') as Variant 
                                        from (select itemid, value as feature 
                                                    from var_items vi 
                                                    join var_values vv on vv.id = vi.varid 
                                                    join var_types vt on vt.id = vv.type_id 
                                                    where vi.itemid = pd.id ) as f group by itemid),'') as Name
                                        , p.id, round(v.dim1*v.dim2*v.dim3/1728,2) volume, 
                                v.weight 
                                from product_details pd 
                                join product p on p.id = pd.pid left 
                                join v_item_weight v on v.id =pd.id 
                                where p.manufacturer_id in (3567,13741) and pd.id in ({Finalized_items_list}) ''')
    y = db.fetchall()
    for j in range(len(y)):
        y[j] = list(y[j])
    db.close()
    sql.close()

    item_list = pd.DataFrame(columns=['itemid','p_name','item_name','pid','volume','weight'])
    item_list[['itemid','p_name','item_name','pid','volume','weight']] = y
    sql = sqlconnect(uid, pwd)
    db = sql.cursor()
    query = f'''with iidf as (select poi.itemid, f.name, ROW_NUMBER() over(partition by poi.itemid order by po.ts desc) rn 
                    from purchase_order po 
                    join purchase_order_items poi on po.id = poi.poid 
                    join product_details pd on pd.id = poi.itemid 
                    join product p on p.id = pd.pid 
                    join factory f on f.id = po.factory_id 
                    where  p.manufacturer_id in (3567,13741) and factory_id is not null and pd.id in ({Finalized_items_list}) ) select itemid, name 'factory' from iidf where rn = 1'''
    if uid == 'Pirrabhu':
        query = f'''with iidf as (select poi.itemid, f.name, ROW_NUMBER() over(partition by poi.itemid order by po.ts desc) rn 
                        from purchase_order po 
                        join purchase_order_items poi on po.id = poi.poid 
                        join product_details pd on pd.id = poi.itemid 
                        join product p on p.id = pd.pid 
                        join factory f on f.id = po.factory_id 
                        where  p.manufacturer_id in (3567,13741) and factory_id is not null and pd.id in ({Finalized_items_list}) ) select itemid, name 'factory' from iidf where rn = 1'''
    a1 = db.execute(query)
    y = db.fetchall()
    for j in range(len(y)):
        y[j] = list(y[j])
    db.close()
    sql.close()

    item_factory = pd.DataFrame(columns=['itemid','factory'])
    item_factory[['itemid','factory']] = y
    item_list = pd.merge(item_list,item_factory,'left')
    dt.tables('item_list').update(item_list,index=False)

    lq = wb.sheets('Loading Qty').range('lq[#All]').options(pd.DataFrame,index=False).value
    lq.dropna(subset=['itemid'],inplace=True)
    lq['itemid'] = lq['itemid'].astype(int)
    item_list[['volume','weight']] = item_list[['volume','weight']].fillna(0)
    item_list['max_qty'] = item_list[['itemid','volume','weight']].apply(lambda x:0 if x[1]==0 or x[2]==0 else min(floor(2400/x[1]),floor(50000/x[2])) if x[0] not in lq.itemid.tolist() else lq.loc[lq['itemid']==x[0],'qty'].values[0],axis=1)
    item_list['min_qty'] = item_list['max_qty'].apply(lambda x:0 if x==0 else 25 if x<100 else 50 if x<500 else 100 if x<100 else 200)
    item_list['volume'] = item_list[['itemid','volume']].apply(lambda x:round(2400/lq.loc[lq['itemid']==x[0],'qty'].values[0],1) if x[0] in lq.itemid.tolist() else x[1],axis=1)
    item_list['weight'] = item_list[['itemid','weight']].apply(lambda x:round(50000/lq.loc[lq['itemid']==x[0],'qty'].values[0],1) if x[0] in lq.itemid.tolist() else x[1],axis=1)



# %%

def po_transit_data():
    global po_transit

    transit = pd.DataFrame({'port':['Dalian','FUQING','Fuzhou','Guangzhou','Itapoá','Lianyungang','Nanjing','Nansha','Navegantes','Ningbo','Qingdao','Shanghai','Shenzhen','Wuhan','Xiamen','Xingang','Yantian','Dalian','FUQING','Fuzhou','Guangzhou','Itapoá','Lianyungang','Nanjing','Nansha','Navegantes','Ningbo','Qingdao','Shanghai','Shenzhen','Wuhan','Xiamen','Xingang','Yantian','Dalian','FUQING','Fuzhou','Guangzhou','Itapoá','Lianyungang','Nanjing','Nansha','Navegantes','Ningbo','Qingdao','Shanghai','Shenzhen','Wuhan','Xiamen','Xingang','Yantian','Semarang','Semarang','Semarang','Kwun Tong','Shenzhen','Qingdao','Fuzhou','Xiamen','Ningbo','Dalian','FUQING','Guangzhou','Itapoá','Lianyungang','Nanjing','Nansha','Navegantes','Shanghai','Wuhan','Xingang','Yantian','Semarang','Shenzhen','Qingdao','Fuzhou','Xiamen','Ningbo','Dalian','FUQING','Guangzhou','Itapoá','Lianyungang','Nanjing','Nansha','Navegantes','Shanghai','Wuhan','Xingang','Yantian','Semarang','Tanjung','Tanjung']
        ,'warehouse':['FCI','FCI','FCI','FCI','FCI','FCI','FCI','FCI','FCI','FCI','FCI','FCI','FCI','FCI','FCI','FCI','FCI','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','World Depot','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','TRAFFIC TECH','World Depot','FCI','TRAFFIC TECH','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','QX Logistix','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','Arms Logistics','FCI']
        ,'transit':[68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,55,45,38,45,58,45,45,45,58,45,45,52,39,45,42,45,40,55,45,38,45,58,45,45,45,58,45,45,52,39,45,42,45,40,45,45,45,35,35,35,35,35,35,55,45,45,58,45,45,45,45,52,45,45,40,45,35,35,35,35,35,55,45,45,58,45,45,45,45,52,45,45,40,45,45,50]})

    sql = sqlconnect(uid, pwd)
    db = sql.cursor()
    query = f'''select distinct po.ref_number, por.name port,w.name warehouse 
                    from purchase_order po 
                    join purchase_order_items poi on po.id = poi.poid 
                    join product_details pd on pd.id = poi.itemid 
                    join product p on p.id = pd.pid 
                    join warehouse w on w.id = po.po_warehouse_id 
                    join factory f on f.id = po.factory_id  
                    join factoryports fp on fp.FactoryId = f.id 
                    join ports por on por.id = fp.portid 
                    where  p.manufacturer_id in (3567,13741) and factory_id is not null and pd.id in ({Finalized_items_list}) and (po.status is null or po.status='')'''
    if uid=='Pirrabhu':
        query = f'''select distinct po.ref_number, por.name port,w.name warehouse 
        from purchase_order po 
        join purchase_order_items poi on po.id = poi.poid 
        join product_details pd on pd.id = poi.itemid 
        join product p on p.id = pd.pid 
        join warehouse w on w.id = po.po_warehouse_id 
        join factory f on f.id = po.factory_id  
        join factoryports fp on fp.FactoryId = f.id 
        join ports por on por.id = fp.portid 
        where  p.manufacturer_id in (3567,13741) and factory_id is not null and pd.id in ({Finalized_items_list}) and (po.status is null or po.status='')'''
    a1 = db.execute(query)
    y = db.fetchall()
    for j in range(len(y)):
        y[j] = list(y[j])
    db.close()
    sql.close()

    po_transit = pd.DataFrame(columns=['ref_number','port','warehouse'])
    po_transit[['ref_number','port','warehouse']] = y
    po_transit = pd.merge(po_transit,transit,left_on=['port','warehouse'],right_on=['port','warehouse'],how='left')
    po_transit.index = po_transit.ref_number.values
    dt.tables('po_transit').update(po_transit,index=False)


# %%

def po_data():
    global pocols,podf,po_date
    sql = sqlconnect(uid, pwd)
    db = sql.cursor()

    a1 = db.execute(f"""select po.ref_number, poi.itemid, poi.qty,cast(po.po_expected_date as date) expected_date, f.name, po.ltl_waybill_ref 
                    from purchase_order po 
                    join purchase_order_items poi with(nolock) on po.id = poi.poid 
                    join product_details pd with(nolock) on pd.id = poi.itemid 
                    join product p with(nolock) on p.id = pd.pid 
                    join warehouse w with(nolock) on w.id = po.po_warehouse_id 
                    join factory f with(nolock) on f.id = po.factory_id 
                    where  p.manufacturer_id in (3567,13741) and factory_id is not null and pd.id in ({Finalized_items_list}) and (po.status is null or po.status='') order by expected_date""")
    y = db.fetchall()
    for j in range(len(y)):
        y[j] = list(y[j])
    db.close()
    sql.close()
    pocols = ['ref_number', 'itemid', 'qty', 'expected_date', 'factory', 'ltl_waybill_ref']
    podf = pd.DataFrame(columns =pocols)
    podf[pocols] = y
        
    po_date = podf[['ref_number','expected_date']].drop_duplicates().reset_index(drop=True)
    for i in podf['ref_number'].tolist():
        valid_exp_date = datetime.date.today()+datetime.timedelta(ceil(po_transit[po_transit['ref_number']==i]['transit'].tolist()[0]/7)*7)
        po_exp_date = podf[podf['ref_number']==i]['expected_date'].tolist()[0]
        if podf[podf['ref_number']==i]['ltl_waybill_ref'].tolist()[0] is None and po_exp_date < (valid_exp_date):
            podf.loc[podf['ref_number']==i,'expected_date'] = valid_exp_date
        

    podf.drop_duplicates(inplace=True)
    podf = podf.sort_values(["expected_date",'ltl_waybill_ref','ref_number']).reset_index(drop=True)
    for i in range(len(wcols)):
        podf.loc[:,wcols[i]] = 0
        podf.loc[podf['expected_date'].apply(wnum)==wcols[i],wcols[i]] = podf.loc[podf['expected_date'].apply(wnum)==wcols[i],'qty']
        # podf[wcols[i]] = 0
        # for j in range(len(podf)):
        #     if wnum(podf.loc[j,'expected_date'])==wcols[i]:
        #         podf.at[j,wcols[i]] = podf.at[j,'qty']
    podf.fillna('',inplace=True)
    podf['expected_date'] = podf['expected_date'].apply(lambda x:x.date() if type(x)!=datetime.date else x)
    wb.sheets('Purchase Orders').tables('podf').update(podf,index=False)
    wb.sheets('Purchase Orders').tables('po_date').update(po_date,index=False)


# %%
def inventory_data():
    global invdf, inv, presell_dict
    
    inv = pd.DataFrame(columns=['item_id', 'qty', 'Warehouse_id','in_queue'])
    sql = sqlconnect(uid, pwd)
    db = sql.cursor()

    a1 = db.execute(f'''select  itemid,	case when available_qty <0 then 0 else available_qty end as qty,vendor_id, case when available_qty<=0 then queue_qty else 0 end as queue_qty
                        from product_details_warehouse_inventory
                        where itemid in ({Finalized_items_list}) and (available_qty >0 or queue_qty>0)''')
    y = db.fetchall()
    for j in range(len(y)):
        y[j] = list(y[j])
    db.close()
    sql.close()
    inv[inv.columns] = y
    invdf = inv[['item_id','qty','in_queue']].fillna(0).groupby('item_id',as_index=False).sum()
    presell_dict = dict(invdf.loc[(invdf['in_queue']>0)&(invdf['qty']==0),['item_id','in_queue']].astype(int).values)
    wb.sheets('Inventory').tables('invdf').update(invdf,index=False)

def get_data_from_sheet():
    global item_list,po_transit, adfc1, podf,pocols,po_date, invdf, cl_inv, margin,iidlist,PO_Summary,china_sheet,stat_dict,color_dict
    #Get Data From Sheet - Item List
    item_list = wb.sheets('Data').range('item_list[#All]').options(pd.DataFrame,index=False).value
    item_list['itemid'] = item_list['itemid'].astype(int)

    lq = wb.sheets('Loading Qty').range('lq[#All]').options(pd.DataFrame,index=False).value
    lq.dropna(subset=['itemid'],inplace=True)
    lq['itemid'] = lq['itemid'].astype(int)
    item_list[['volume','weight']] = item_list[['volume','weight']].fillna(0)
    item_list['max_qty'] = item_list[['itemid','volume','weight']].apply(lambda x:0 if x[1]==0 or x[2]==0 else min(floor(2400/x[1]),floor(50000/x[2])) if x[0] not in lq.itemid.tolist() else lq.loc[lq['itemid']==x[0],'qty'].values[0],axis=1)
    item_list['min_qty'] = item_list['max_qty'].apply(lambda x:0 if x==0 else 25 if x<100 else 50 if x<500 else 100 if x<100 else 200)
    item_list['volume'] = item_list[['itemid','volume']].apply(lambda x:round(2400/lq.loc[lq['itemid']==x[0],'qty'].values[0],1) if x[0] in lq.itemid.tolist() else x[1],axis=1)
    item_list['weight'] = item_list[['itemid','weight']].apply(lambda x:round(50000/lq.loc[lq['itemid']==x[0],'qty'].values[0],1) if x[0] in lq.itemid.tolist() else x[1],axis=1)

    #Get Data From Sheet - PO_Transit
    po_transit = wb.sheets('Data').range('po_transit[#All]').options(pd.DataFrame,index=False).value
    po_transit['ref_number'] = po_transit['ref_number'].astype(str).apply(lambda x:x.replace('.0','')).tolist()

    #Get Data From Sheet - Sales FC Assembly
    adfc1 = wb.sheets('Sales_FC_Assembly').range('adfc1[#All]').options(pd.DataFrame,index=False).value
    adfc1['itemid'] = adfc1['itemid'].astype(int)
    adfc1.index=adfc1['itemid']
    iidlist = adfc1.itemid.astype(int).tolist()
    wcols = list(adfc1.columns[1:-1])

    #Get Data From Sheets Purchase Orders
    podf = wb.sheets('Purchase Orders').range('podf[#All]').options(pd.DataFrame,index=False).value
    podfcols = wb.sheets('Purchase Orders').range('podf[#Headers]').value
    podf['ref_number'] = podf['ref_number'].apply(lambda x:str(x).replace('.0',''))
    podf['expected_date'] = podf['expected_date'].apply(lambda x:x.date() if type(x)!=datetime.date else x)
    pocols = podfcols[:6]

    #Get Data From Sheet PO Original Date
    po_date = wb.sheets('Purchase Orders').range('po_date[#All]').options(pd.DataFrame,index=False).value
    po_date['ref_number'] = po_date['ref_number'].apply(lambda x:str(x).replace('.0',''))
    po_date['expected_date'] = po_date['expected_date'].apply(lambda x:x.date())

    #Get Data From Sheets - Inventory
    invdf = wb.sheets('Inventory').range('invdf[#All]').options(pd.DataFrame,index=False).value
    invdf['item_id'] = invdf['item_id'].astype(int)

    #Get Data From Sheets - Closing Inventory
    cl_inv = wb.sheets('Closing Inventory').range('cl_inv[#All]').options(pd.DataFrame,index=False).value
    cl_inv['itemid'] = cl_inv['itemid'].astype(int)

    #Get Data From Sheets - Margin
    margin = wb.sheets('Data').range('margin[#All]').options(pd.DataFrame,index=False).value
    margin['id'] = margin['id'].astype(int)
    margin['margin'] = margin['margin'].astype(float)
    margin['cost'] = margin['cost'].astype(float)
    margin['price'] = margin['price'].astype(float)

    statuses = ['Space Released','Hold','Container Booked','Ready at Factory']
    color_dict = {'Space Released':pink,'Hold':blue,'Container Booked':purple,'Ready at Factory':cream}
    china_sheet = wb.sheets['China_Sheet'].range('china_sheet[#All]').options(pd.DataFrame,index=False).value
    china_sheet['PO'] = china_sheet['PO'].apply(lambda x:str(x).replace('.0',''))
    china_sheet = china_sheet[['PO','Ready Date','Hold','Status of the PO','Actual Ship Date']]
    china_sheet = china_sheet.reset_index(drop=True)
    china_sheet['Ready Date'] = china_sheet['Ready Date'].apply(lambda x:x.date() if (x is not None) and type(x)!=str else None)
    china_sheet['Actual Ship Date'] = china_sheet['Actual Ship Date'].apply(lambda x:x.date() if (x is not None) and type(x)!=str else None)
    china_sheet[['date','Status']] = china_sheet[['Actual Ship Date','Ready Date','Status of the PO','Hold']].apply(lambda x:[x[0],'Space Released'] if x[0] not in [pd.NaT,None] else [x[1],'Hold'] if x[3]=='Hold' else [x[1],x[2]] if x[2] in statuses else [x[1],None],axis=1).tolist()
    stat_dict = {}
    for i in china_sheet.index:
        stat_dict[china_sheet.loc[i,'PO']] = {'date':china_sheet.loc[i,'date'],'status':china_sheet.loc[i,'Status']}

def pre_recalc():
    global new_po_added,podf, PO_Summary
    if new_po_added==True:
        podf = podf[pocols]
        podf = pd.concat([podf,new_pos_df[pocols]])
        podf['expected_date'] = podf['expected_date'].apply(lambda x:x.date() if type(x)!=datetime.date else x)
        podf.reset_index(drop=True,inplace=True)
        for i in range(len(wcols)):
            podf.loc[:,wcols[i]] = 0
            podf.loc[podf['expected_date'].apply(wnum)==wcols[i],wcols[i]] = podf.loc[podf['expected_date'].apply(wnum)==wcols[i],'qty']
            # for j in range(len(podf)):
            #     if wnum(podf.loc[j,'expected_date'])==wcols[i]:
            #         podf.at[j,wcols[i]] = podf.at[j,'qty']
        new_po_added=False
    
    PO_Summary = sh.range("PO_Summary[#All]").options(pd.DataFrame,index=False).value
    PO_Summary['earliest_possible_expected_date'] = PO_Summary['earliest_possible_expected_date'].apply(lambda x:x.date())
    PO_Summary['ref_number'] = PO_Summary['ref_number'].apply(lambda x:str(x).replace('.0',''))

    podf['expected_date'] = pd.merge(podf[['ref_number','expected_date']],PO_Summary.iloc[:,:2],how='left').apply(lambda x:x[1] if pd.isna(x[2]) else x[2],axis=1)    
    # for i in range(len(PO_Summary)):
    #     ref_number = PO_Summary.loc[i,'ref_number']
    #     date = PO_Summary.loc[i,'earliest_possible_expected_date']
    #     poi_count = len(podf[podf['ref_number']==ref_number])
    #     try:
    #         if podf.loc[podf['ref_number']==ref_number,'expected_date'].values[0]!=date:
    #             datelist = [date for j in range(poi_count)]
    #             podf.loc[podf['ref_number']==ref_number,'expected_date'] = datelist
    #             po_date.loc[po_date['ref_number']==ref_number,'expected_date'] = date
    #     except:
    #         ''
    for i in range(len(wcols)):
        podf.loc[:,wcols[i]] = 0
        podf.loc[podf['expected_date'].apply(wnum)==wcols[i],wcols[i]] = podf.loc[podf['expected_date'].apply(wnum)==wcols[i],'qty']
        # podf[wcols[i]] = 0
        # for j in range(len(podf)):
        #     if wnum(podf.loc[j,'expected_date'])==wcols[i]:
        #         podf.at[j,wcols[i]] = podf.at[j,'qty']

#From Sheet
def po_balc():
    global po_bal
    po_bal = podf[pocols].copy()
    po_bal['week'] = po_bal['expected_date'].apply(wnum)
    po_bal = po_bal[po_bal['week'].isin(wcols)]
    po_bal = po_bal[po_bal['itemid'].isin(iidlist)]
    po_bal1 = po_bal.copy()
    po_bal.loc[po_bal.index,'preweek'] = po_bal['week']
    for i in range(preweek):
        po_bal1.loc[po_bal1.index,'preweek'] = po_bal1['week'].apply(lambda x: wcols[wcols.index(x)-1-i] if (wcols.index(x)-1-i) >=0 else np.nan)
        po_bal1.drop(po_bal1.index[po_bal1.preweek.isna()],inplace=True)
        po_bal = pd.concat([po_bal,po_bal1])

    del po_bal1
    po_bal.reset_index(drop=True, inplace=True)

#From Sheet
def reset():
    global havcols, hav, iid_zeros, sav, hrsu, srsu, rsu
    havcols = ['itemid']
    havcols.extend(wcols)
    hav = pd.DataFrame(columns=havcols)
    iid_zeros = np.zeros((len(iidlist),len(havcols)))
    for i in range(len(iidlist)):
        iid_zeros[i][0] = iidlist[i]
    hav['itemid'] = iidlist
    hav.fillna(0,inplace=True)
    sav = hav.copy()
    hrsu = hav.copy()
    srsu = hav.copy()
    rsu = hav.copy()

#From Sheet
def havcalc(week):
    if week == wcols[0]:
        tempdf = pd.DataFrame({'itemid':iidlist})
        tempdf = pd.merge(tempdf,invdf,'left',left_on='itemid',right_on='item_id').drop('item_id',axis=1)
        tempdf.fillna(0, inplace=True)
        tempdf['qty'] = tempdf['qty'].astype(int)
        potemp = podf[['itemid',week]].groupby('itemid').aggregate('sum')
        potemp['itemid'] = potemp.index
        potemp.reset_index(drop=True, inplace=True)
        tempdf = pd.merge(tempdf, potemp, 'left',on='itemid')
        tempdf.fillna(0, inplace=True)
        tempdf[week] = tempdf[week].astype(int)
        tempdf[week] = tempdf[week] + tempdf['qty']
        hav[week] = tempdf[week]
    else:
        lweek = wcols[wcols.index(week)-1]
        hav[week] = hav[lweek] - hrsu[lweek]
        x = po_bal.loc[(po_bal.week==week)&(po_bal.preweek==week),['itemid','qty']].groupby('itemid').aggregate('sum')
        x['itemid'] = x.index
        for qty, item  in x.values:
            hav.loc[hav['itemid']==item,week] = hav.loc[hav['itemid']==item,week] + qty

def hrsucalc(week):
    temp = np.minimum(adfc1[week].values, hav[week].values)
    hrsu[week] = temp

def savcalc(week):
    # lweek = wcols[wcols.index(week)-1]
    # if week!=wcols[0]:
    #     sav[week] = np.maximum(sav[week] - srsu[lweek],0)
    tmpdf = po_bal[(po_bal['preweek']==week)&(po_bal['week']!=po_bal['preweek'])][['itemid','qty']].groupby('itemid').aggregate('sum')
    for id in tmpdf.index:
        sav.loc[sav['itemid']==id,week] = tmpdf.loc[id,'qty']

def srsucalc(week):
    adfc1.reset_index(drop=True,inplace=True)
    srsu[week] = np.minimum(np.ceil((adfc1.loc[:,week] - hrsu.loc[:,week])*presell),sav.loc[:,week]).astype(int)
    adfc1.index = adfc1['itemid'].values

def po_balcalc(week):
    if week!=wcols[0]:
        lweek = wcols[wcols.index(week)-1]
        for item, qty in srsu.loc[srsu[lweek]>0,['itemid', lweek]].sort_values(lweek,ascending=False).values:
            if qty>0:
                for index in po_bal.loc[(po_bal['itemid']==item)&(po_bal['preweek']>=week)].index:
                    dec_val = min(po_bal.loc[index,'qty'],qty)
                    new_val = po_bal.loc[index,'qty'] - dec_val
                    po_num = po_bal.loc[index,'ref_number']
                    count = len(po_bal[(po_bal['ref_number']==po_num)&(po_bal['itemid']==item)&(po_bal['preweek']>lweek)])
                    po_bal.loc[(po_bal['ref_number']==po_num)&(po_bal['itemid']==item)&(po_bal['preweek']>lweek),'qty'] = np.array([new_val for pos in range(count)])
                    qty = max(qty - dec_val,0)
            else:
                continue

#Soft Availability Calculation
def savc():
    po_bal['qty'] = po_bal['qty'].astype(int)
    for week in wcols:
        tmpdf = po_bal[(po_bal['preweek']==week)&(po_bal['week']!=po_bal['preweek'])][['itemid','qty']].groupby('itemid').aggregate('sum')
        for id in tmpdf.index:
            sav.loc[sav['itemid']==id,week] = tmpdf.loc[id,'qty']

#Closing Inventory Calculation
def cl_inv_calc():
    global cl_inv
    cl_inv = pd.DataFrame({'itemid':iidlist})
    for i in wcols:
        tempdf = podf[['itemid',i]]
        tempdf = tempdf.groupby('itemid').aggregate('sum')
        tempdf['itemid'] = tempdf.index
        tempdf.reset_index(drop=True,inplace=True)
        
        if wcols.index(i)==0:
            cl_inv[i] = pd.merge(cl_inv,invdf.rename(columns={'item_id':'itemid'}),'left').fillna(0)['qty'] + pd.merge(cl_inv,tempdf,'left').fillna(0)[i] - rsu[i]
        else:
            cl_inv[i] = cl_inv[wcols[wcols.index(i)-1]].values + pd.merge(cl_inv,tempdf,'left').fillna(0)[i] - rsu[i]

def woi_data():
    global woi, country
    adfc1.reset_index(drop=True,inplace=True)
    woi = pd.DataFrame(columns=cl_inv.columns)
    woi['itemid'] = cl_inv['itemid'].copy()
    for i in range(len(wcols)):
        wnum = i
        wlist = wcols[max(wnum-3,0):min(wnum+5,len(wcols)-1)]
        woi[wcols[i]] = np.maximum(np.around(cl_inv[wcols[wnum]]/(adfc1[wlist].sum(axis=1)/len(wlist)),1),0).replace([np.inf,-np.inf],0)
    adfc1.index = adfc1.itemid

    country = wb.sheets('Data').range('country[#All]').options(pd.DataFrame,index=False).value
    
    woi.reset_index(drop=True,inplace=True)
    woi.fillna(0,inplace=True)
    woi = pd.merge(woi,pd.merge(item_list,country)[['itemid','p_name','item_name','factory','country']],how='left')
    wb.sheets('Weeks of Inventory').range('woi').color = (255,255,255)
    wb.sheets('Weeks of Inventory').range('woi').font.bold = False
    
def update_woi():
    wb.sheets('Weeks of Inventory').tables('woi').update(woi,index=False)
    wb.sheets('Weeks of Inventory').range('woi').color = (255,255,255)
    wb.sheets('Weeks of Inventory').range('woi').font.bold = False
    poinc = pd.merge(pd.DataFrame({'itemid':iidlist}),podf[havcols].fillna(0).groupby('itemid').aggregate(sum),on='itemid',how='left')
    rng = wb.sheets('Weeks of Inventory').range('woi')
    val = woi[wcols].values
    for i in range(val.shape[0]):
        for j in range(val.shape[1]):
            if podf.loc[(podf['itemid']==iidlist[i]),wcols[j]].sum()>0:
                rng[i,j+1].font.bold = True
            if val[i,j]>8:
                rng[i,j+1].color = (255, 199, 206)


# Update tables in sheet
def update():
    tbllist = ['podf','rsu','hrsu','srsu','hav','sav','cl_inv','invdf','woi','adfc1','po_date']
    for sht in wb.sheets:
        for tbl in sht.tables:
            if tbl.name in tbllist:
                tbl.update(eval(tbl.name),index=False)
                print(f'Updated: {tbl.name}')


#From Sheet
def recalc(update_data=False):
    if is_refresh==False:
        pre_recalc()
    po_balc()
    reset()
    savc()
    for week in wcols:
        po_balcalc(week)
        havcalc(week)
        hrsucalc(week)
        savcalc(week)
        srsucalc(week)
    rsu[wcols] = hrsu[wcols] + srsu[wcols]
    if update_data:
        lost_sales = pd.DataFrame({'itemid':iidlist})
        lost_sales[wcols] = adfc1[wcols] - rsu[wcols]
        cl_inv_calc()
        woi_data()
        update()



# %%

selected_items = []
#Load Selection

def load_sel():
    global PO_Summary, selected_items,selected_iids
    if len(selected_items)==0 and poval=='':
        selected_iids = item_list.loc[item_list['factory']==fval,'itemid'].tolist()
    elif poval!='':
        selected_iids = podf.loc[podf['ref_number']==poval,'itemid'].tolist()
    else:
        selected_iids = item_list.loc[item_list['p_name'].isin(selected_items),'itemid'].astype(int).tolist()

    selected_iids_index = [iidlist.index(i) for i in selected_iids if i in iidlist]
    if len(selected_iids_index)==0:
        root=Tk()
        root.withdraw()
        messagebox.showerror("No Forecast!","Selected Items do not have a Forecast")
        root.mainloop()
        return ''

    selected_iids = [iidlist[i] for i in selected_iids_index]
    PO_Summary = podf.loc[(podf['factory']==fval)&(podf['itemid'].isin(selected_iids)),['ref_number','expected_date']].rename(columns={'expected_date':'earliest_possible_expected_date'}).drop_duplicates().reset_index(drop=True)
    if type(PO_Summary.iloc[0,1])!=datetime.date:
        PO_Summary['earliest_possible_expected_date'] = PO_Summary['earliest_possible_expected_date'].apply(lambda x:x.date())
    PO_Summary['suggested_date'] = ''
    PO_Summary[' '] = ''
    if len(PO_Summary)<1:
        root=Tk()
        root.withdraw()
        messagebox.showerror("No PO's","No Active PO's under this Factory")
        root.mainloop()
        return ''
    PO_Item_Summary = pd.merge(item_list.loc[item_list['itemid'].isin(selected_iids),['itemid','item_name']],cl_inv)
    po_details = pd.merge(podf.loc[podf['ref_number'].isin(PO_Summary.ref_number.tolist()),['ref_number','itemid','qty']],po_transit[['ref_number','warehouse']],how='left').drop_duplicates()

    margin.index = margin.id.astype(int)

    lost_sales = pd.DataFrame({'itemid':selected_iids})
    lost_sales[wcols] = adfc1.loc[selected_iids,wcols].reset_index(drop=True) - rsu.loc[selected_iids_index,wcols].reset_index(drop=True)
    t_cost = pd.DataFrame({'itemid':selected_iids})
    t_rsv = pd.DataFrame({'itemid':selected_iids})
    t_op_cost = pd.DataFrame({'itemid':selected_iids})
    #cl_inv.loc[selected_iids_index,wcols].values.T instead of rsu.loc[selected_iids_index,wcols].values.T for t_cost
    t_cost[wcols] = (rsu.loc[selected_iids_index,wcols].values.T * margin.loc[selected_iids,'cost'].values).T
    t_rsv[wcols] = (rsu.loc[selected_iids_index,wcols].values.T * margin.loc[selected_iids,'price'].values).T
    t_op_cost[wcols] = (lost_sales[wcols].values.T * margin.loc[selected_iids,'price'].values).T

    cost_summary = pd.concat([pd.DataFrame(t_cost.sum().values.reshape(1,len(wcols)+1),columns=t_cost.columns),
    pd.DataFrame(t_op_cost.sum().values.reshape(1,len(wcols)+1),columns=t_cost.columns),
    pd.DataFrame(t_rsv.sum().values.reshape(1,len(wcols)+1),columns=t_cost.columns)]).reset_index(drop=True).rename(columns={'itemid':''})
    cost_summary[''] = ['Cost','Opportunity Cost','Realisable Sales Value']
    cost_summary.loc[3,:] = 0
    cost_summary.loc[3,''] = 'Margin'
    cost_summary.loc[3,wcols] = ((cost_summary.loc[2,wcols]*margin_est) - (cost_summary.loc[0,wcols]*storage_est)).values

    try:
        sh.range('PO_Summary').delete('up')
        sh.range('po_details').delete('up')
    except:
        ''
    if sh.tables['po_details'].range.row < (len(PO_Summary)+sh.tables['PO_Summary'].range.row+5):
        nrows = (len(PO_Summary)+sh.tables['PO_Summary'].range.row+5) - sh.tables['po_details'].range.row
        for i in range(nrows):
            sh.range(f'''A{sh.tables['po_details'].range.row-1}:E{sh.tables['po_details'].range.row-1}''').insert('down')

    # Load Update in Sheet

    sh.range('PO_Summary').color = (255,255,255)
    sh.range('PO_Item_Summary').color = (255,255,255)
    sh.range('po_details').color = (255,255,255)
    sh.range('cost_summary').color = (255,255,255)

    sh.range('PO_Summary').font.color = (0,0,0)
    sh.range('PO_Item_Summary').font.color = (0,0,0)
    sh.range('po_details').font.color = (0,0,0)
    sh.range('cost_summary').font.color = (0,0,0)

    sh.tables['PO_Summary'].update(PO_Summary,index=False)
    sh.tables['po_details'].update(po_details,index=False)

    try:
        sh.range('PO_Item_Summary').delete('up')
    except:
        ''

    if sh.tables['cost_summary'].range.row < (len(PO_Item_Summary)+sh.tables['PO_Item_Summary'].range.row+5):
        nrows = (len(PO_Item_Summary)+sh.tables['PO_Item_Summary'].range.row+5) - sh.tables['cost_summary'].range.row
        for i in range(nrows):
            sh.range(f'''G{sh.tables['cost_summary'].range.row-1}:BI{sh.tables['cost_summary'].range.row-1}''').insert('down')
    sh.tables['PO_Item_Summary'].update(PO_Item_Summary.rename(columns=wk_to_dt),index=False)
    sh.tables['cost_summary'].update(cost_summary,index=False)


    #Con_Format - PO_Summary
    templist = []
    for cell in sh.range('PO_Summary[ref_number]'):
        ref_num = str(int(cell.value)) if type(cell.value)!=str else str(cell.value)
        
        try:
            if po_transit[po_transit['ref_number']==ref_num].values[0,2]!='FCI':
                cell.color = (128,128,128)
            else:
                cell.color = (255,204,153)
            if podf.loc[podf['ref_number']==ref_num,'ltl_waybill_ref'].values[0] not in (None,''):
                sh.range(cell.offset(0,1),cell.offset(0,3)).color = (198, 239, 206)
                sh.range(cell.offset(0,1),cell.offset(0,3)).font.color = (0, 97, 0)
            else:
                if ref_num in stat_dict.keys():
                    
                    if stat_dict[ref_num]['status'] is not None:
                        sh.range(cell.offset(0,1),cell.offset(0,3)).color = color_dict[stat_dict[ref_num]['status']]
                    cell.offset(0,3).value = stat_dict[ref_num]['date']
                
                if cell.offset(0,1).value.date()!= po_date.loc[po_date['ref_number']==ref_num,'expected_date'].values[0]:
                    cell.offset(0,1).color = (255, 165, 0)
                    cell.offset(0,1).font.color = (0, 97, 0)
                    templist.append(ref_num)
                    
        except:
            ''

    #Con_Format - PO_Details

    for cell in sh.range('po_details[ref_number]'):
        ref_num = str(int(cell.value)) if type(cell.value)!=str else str(cell.value)
        try:

            if podf.loc[podf['ref_number']==ref_num,'ltl_waybill_ref'].values[0] not in (None,''):
                sh.range(cell,cell.offset(0,3)).color = (198, 239, 206)
                sh.range(cell,cell.offset(0,3)).font.color = (0, 97, 0)
            else:
                if ref_num in stat_dict.keys():
                    sh.range(cell,cell.offset(0,3)).color = color_dict[stat_dict[ref_num]['status']]
                if ref_num in templist:
                    cell.offset(0,1).color = (255, 165, 0)
                    cell.offset(0,1).font.color = (0, 97, 0)

        except:
            ''

    #Con_Format PO_Item_Summary
    for item in PO_Item_Summary.itemid:
        
        fclist = adfc1.loc[item,wcols]
        rn = PO_Item_Summary.itemid.tolist().index(item)+3
        
        for week in wcols:
            val = PO_Item_Summary.loc[rn-3,week]
            ow = fclist[wcols[min(wcols.index(week)+1,len(wcols)-1)]].sum()
            if val >ow:
                cnum = 9+wcols.index(week)
                w_num = wcols.index(week)
                
                fw = fclist[wcols[min(w_num+1,len(wcols)-1):min(w_num+5,len(wcols)-1)]].sum()
                ew = fclist[wcols[min(w_num+1,len(wcols)-1):min(w_num+9,len(wcols)-1)]].sum()
                
                if val<=fw:
                    sh.cells(rn,cnum).font.color = (0, 97, 0)
                    sh.cells(rn,cnum).color = (198, 239, 206)
                elif val<=ew:
                    sh.cells(rn,cnum).font.color = (156, 87, 0)
                    sh.cells(rn,cnum).color = (255, 235, 156)
                else:
                    sh.cells(rn,cnum).font.color = (156, 0, 6)
                    sh.cells(rn,cnum).color = (255, 199, 206)



# %%
# Create New PO
def create_po():
    global croot, new_po_items
    def open_popup():
        global create_po_date
        create_po_date = ''
        def get_date():
            global create_po_date
            create_po_date = cal.get_date()
            dt_tbox.config(text=create_po_date)
            create_po_date = pd.Timestamp(datetime.datetime.strptime(create_po_date,"%m/%d/%y"))
            top.destroy()
        top= Toplevel(croot)
        top.geometry("220x250")
        top.title("Select")
        cal = Calendar(top, selectmode = 'day')
        cal.pack(pady=20)
        btn = Button(top,text='Select',command= get_date)
        btn.pack()
        
    new_po_items = ''

    def get_item_details(event,idx):
        global datum,item
        item = int(comboboxes[idx].get())
        datum = factory_po_data[factory_po_data['itemid']==item]
        data[idx] = datum
        min_q_val = str(datum['min_qty'].values[0])
        max_q_val = str(datum['max_qty'].values[0])
        i_name_val = str(datum['item_name'].values[0])
        min_qtys[idx].config(text=min_q_val)
        max_qtys[idx].config(text=max_q_val)
        item_names[idx].config(text=i_name_val)

    def c_factory_selected(event):
        global fval, new_po_items, factory_po_data
        fval = fn.get()
        new_po_items = ['']
        factory_po_data = item_list.loc[item_list['factory']==fval,['itemid','min_qty','max_qty','item_name','volume','weight']]
        factory_po_data[['itemid','min_qty','max_qty']] = factory_po_data[['itemid','min_qty','max_qty']].astype(int)
        new_po_items.extend(factory_po_data.itemid.astype(int).drop_duplicates().tolist())
        for i in comboboxes:
            i.config(values=new_po_items)


    def add_group():
        # # Create a new group of widgets
        # group_frame = ttk.Frame(croot, padding=10)
        # group_frame.grid(column=0, row=len(groups), pady=10)

        if len(groups)==0:
            Label(croot,text='ItemId', borderwidth=1, relief="raised",width=20).grid(row=3,column=0,pady=5)
            Label(croot,text='Min Qty', borderwidth=1, relief="raised",width=20).grid(row=3,column=1,pady=5)
            Label(croot,text='Max Qty', borderwidth=1, relief="raised",width=20).grid(row=3,column=2,pady=5)
            Label(croot,text='Req Qty', borderwidth=1, relief="raised",width=20).grid(row=3,column=3,pady=5)

        # Create a new group of widgets
        group_frame = ttk.Frame(croot, padding=5)
        group_frame.grid(column=0, row=len(groups)+5, pady=5,columnspan=4)

        # Create the combobox
        item_cb = ttk.Combobox(group_frame)
        item_cb.config(values=new_po_items)
        item_cb.grid(column=0, row=0, padx=5)
        item_cb.bind("<<ComboboxSelected>>", lambda event, idx=len(groups): get_item_details(event, idx))

        # Create the labels
        min_qty = ttk.Label(group_frame, text="", borderwidth=1, relief="raised",width=20)
        min_qty.grid(column=1, row=0, padx=5)
        max_qty = ttk.Label(group_frame, text="", borderwidth=1, relief="raised",width=20)
        max_qty.grid(column=2, row=0, padx=5)

        # Create the o_qty
        o_qty = ttk.Entry(group_frame, width=20)
        o_qty.grid(column=3, row=0, padx=5)

        # Create the Item Name label

        item_name = ttk.Label(group_frame, text="", borderwidth=1, relief="raised",width=80)
        item_name.grid(column=0,row=1,columnspan=4,pady=5)

        # Add the widgets to the respective lists
        comboboxes.append(item_cb)
        min_qtys.append(min_qty)
        max_qtys.append(max_qty)
        o_qtys.append(o_qty)
        item_names.append(item_name)
        data.append('')

        # Update the counter
        groups.append(group_frame)


    def calculate():
        global new_pos_df,calculated
        calculated=False
        new_pos_df = pd.concat([i for i in data if type(i)==pd.DataFrame])
        
        qtys = [int(i.get()) for i in o_qtys if i.get()!='']
        factories = [fval for i in qtys]
        ref_num = 'PO' + datetime.datetime.now().strftime('%d%m%y%H%M%S')
        ref_nums = [ref_num for i in qtys]
        ltls = [None for i in qtys]
        dates = [create_po_date for i in qtys]
        new_pos_df['qty'] = qtys
        new_pos_df['factory'] = factories
        new_pos_df['ref_number'] = ref_nums
        new_pos_df['expected_date'] = dates
        new_pos_df['ltl_waybill_ref'] = ltls
        t_volume = (new_pos_df['volume'] * new_pos_df['qty']).sum()
        t_r_volume = 2400 - t_volume
        t_r_volume_lbl.config(text=str(int(t_r_volume))+' cft')
        t_weight = (new_pos_df['weight'] * new_pos_df['qty']).sum()
        t_r_weight = 50000 - t_weight
        t_r_weight_lbl.config(text=str(int(t_r_weight))+' lbs')
        if t_r_weight<0:
            messagebox.showerror('Capacity Exceeded','Container Weight Limit Exceeded')
        if t_r_volume<0:
            messagebox.showerror('Capacity Exceeded','Container Volume Limit Exceeded')
        calculated=True

    def generate_po():
        global new_po_added
        if calculated==False:
            ctroot=Tk()
            ctroot.withdraw()
            messagebox.showerror("Calculate before generating PO")
            ctroot.mainloop()
        else:
            #Add New PO to podf
            croot.destroy()
            new_po_added = True

    croot = tk.Tk()
    croot.title("Create PO's")

    # Create lists to store the widgets
    comboboxes = []
    min_qtys = []
    max_qtys = []
    o_qtys = []
    groups = []
    item_names = []
    data = []


    fn_lbl = Label(croot,text='Select Factory', borderwidth=1, relief="raised")
    fn_lbl.grid(row=0, column=0, padx=2, pady=2)

    fn = ttk.Combobox(croot, values=item_list['factory'].sort_values().drop_duplicates().tolist(),width=60)
    fn.grid(row=0, column=1, padx=10, pady=10,columnspan=3)
    fn.bind('<<ComboboxSelected>>',c_factory_selected)

    dt_btn = Button(croot,text='Select PO Expected Date',command=open_popup)
    dt_btn.grid(row=1, column=0,columnspan=1,padx=5)

    dt_tbox = Label(croot,text='m/d/yy', borderwidth=1, relief="raised")
    dt_tbox.grid(row=1, column=1,columnspan=1)


    calc = ttk.Button(croot, text="Calculate", command=calculate)
    calc.grid(row=1, column=2,columnspan=1)

    # Create a button to add another group
    add_button = ttk.Button(croot, text="Add Item", command=add_group)
    add_button.grid(column=3, row=1, pady=10,columnspan=4)
    Label(croot,text='Remaining Weight', borderwidth=1, relief="raised").grid(row=2,column=0,pady=10)
    t_r_weight_lbl = Label(croot,text='50000', borderwidth=1, relief="raised")
    t_r_weight_lbl.grid(row=2,column=1,pady=10)
    Label(croot,text='Remaining Volume', borderwidth=1, relief="raised").grid(row=2,column=2,pady=10)
    t_r_volume_lbl = Label(croot,text='2400', borderwidth=1, relief="raised")
    t_r_volume_lbl.grid(row=2,column=3,pady=10)



    generate_po = Button(croot,text='Generate PO',command=generate_po)
    generate_po.grid(row=100,column=0,columnspan=4,pady=5)

    croot.mainloop()



# %%

def login():
    global uid,pwd
    lroot = Tk()
    uid_lbl = Label(lroot,text='SQL User_Id',padx=10,pady=10,width=20)
    uid_lbl.grid(row=0,column=0)
    uid_e = Entry(lroot,width=20)
    uid_e.grid(row=0,column=1)

    pwd_lbl = Label(lroot,text='SQL Password',padx=10,pady=10,width=20)
    pwd_lbl.grid(row=1,column=0)
    pwd_e = Entry(lroot,width=20,show='*')
    pwd_e.grid(row=1,column=1)

    def get_credentials():
        global uid,pwd
        uid = uid_e.get()
        pwd = pwd_e.get()
        lroot.destroy()
        with wb.app.properties(status_bar='Extracting Data from DB 1/8'):
            margin_data()
        with wb.app.properties(status_bar='Extracting Data from DB 2/8'):
            assmbly_data()
        with wb.app.properties(status_bar='Extracting Data from DB 3/8'):
            sort_dfc()
        with wb.app.properties(status_bar='Extracting Data from DB 4/8'):
            fc_assembly_split()
        with wb.app.properties(status_bar='Extracting Data from DB 5/8'):
            item_list_data()
        with wb.app.properties(status_bar='Extracting Data from DB 6/8'):
            po_transit_data()
        with wb.app.properties(status_bar='Extracting Data from DB 7/8'):
            po_data()
        with wb.app.properties(status_bar='Extracting Data from DB 8/8'):
            inventory_data()
        with wb.app.properties(status_bar='Generating Forecast'):
            recalc(True)
    login_btn = Button(lroot,padx=10,pady=10,width=20,text='Refresh Data',command=get_credentials)
    login_btn.grid(row=2,column=0,columnspan=2)

    lroot.mainloop()


# %%
    
def refresh():
    global uid,pwd
    uid,pwd = '',''
    login()


# %%

#Prioritization Filter
def prioritization_filter():
    
    sh.activate()
    try:
        hav.iloc[0,0]
    except:
        get_data_from_sheet()
        recalc(True)
    fval=''
    poval = ''

    def on_button_click():
        global selected_items
        selected_items = []
        for i in lb.curselection():
            selected_items.append(lb.get(i))
        
        load_sel()


    def factory_selected(event):
        global fval,templist,poval
        fval = fn.get()
        poval=''
        templist = ['']
        templist.extend(podf.loc[podf['factory']==fval,'ref_number'].drop_duplicates().tolist())
        po_sel['values'] = templist
        lb.delete(0,END)
        for i in item_list[item_list['factory']==fval].p_name.drop_duplicates().tolist():
            lb.insert(END,i)

    def po_selected(event):
        global poval
        poval = po_sel.get()
        lb.selection_clear(0,'end')

    def recalculate():
        recalc(True)
        load_sel()

    root = tk.Tk()
    root.title("Filters")
    root.grid_rowconfigure(1, weight=1)
    # root.grid_columnconfigure(0, weight=1)

    root.geometry('1050x170')

    yscrollbar = Scrollbar(root)

    # Create the first dropdown menu
    fn_lbl = Label(root,text='Select Factory')
    fn_lbl.grid(row=0, column=0, padx=2, pady=2)
    
    flistx = pd.concat([item_list.loc[item_list['itemid'].isin(adfc1.itemid.values),'factory'],podf['factory']]).sort_values().drop_duplicates().tolist()
#    fn = ttk.Combobox(root, values=podf['factory'].sort_values().drop_duplicates().tolist(),width=60)
    fn = ttk.Combobox(root, values=flistx,width=60)
    fn.grid(row=1, column=0, padx=10, pady=10)
    fn.bind('<<ComboboxSelected>>',factory_selected)

    # Create the second dropdown menu
    po_sel_lbl = Label(root,text='Select PO')
    po_sel_lbl.grid(row=0, column=1, padx=2, pady=2)

    po_sel = ttk.Combobox(root, values=[" "],width=20)
    po_sel.grid(row=1, column=1, padx=10, pady=10)
    po_sel.bind('<<ComboboxSelected>>',po_selected)

    # Create the third dropdown menu
    # dropdown3 = ttk.Combobox(root, values=["Item X", "Item Y", "Item Z"])
    # dropdown3.grid(row=0, column=2, padx=10, pady=10)

    item_sel_lbl = Label(root,text='Select Product')
    item_sel_lbl.grid(row=0, column=2, padx=2, pady=2)

    lb = Listbox(root, selectmode = "multiple", 
                yscrollcommand = yscrollbar.set,width=60,height=4)
    lb.grid(row=1, column=2, padx=10, pady=10,sticky='nsew',rowspan=2)

    x =[" "]
    for each_item in range(len(x)):
        
        lb.insert(END, x[each_item])

    def refresh_start():
        global is_refresh
        # root.config(cursor='watch')
        # root.update()
        # root.after(100,refresh)
        # root.config(cursor='')
        is_refresh=True
        with wb.app.properties(status_bar='Extracting Data from DB 1/10'):
            country_data()
        with wb.app.properties(status_bar='Extracting Data from DB 2/10'):
            po_transit_data()
        with wb.app.properties(status_bar='Extracting Data from DB 3/10'):
            margin_data()
        with wb.app.properties(status_bar='Extracting Data from DB 4/10'):
            assmbly_data()
        with wb.app.properties(status_bar='Extracting Data from DB 5/10'):
            inventory_data()
        with wb.app.properties(status_bar='Extracting Data from DB 6/10'):
            sort_dfc()
        with wb.app.properties(status_bar='Extracting Data from DB 7/10'):
            fc_assembly_split()
        with wb.app.properties(status_bar='Extracting Data from DB 8/10'):
            item_list_data()
        with wb.app.properties(status_bar='Extracting Data from DB 9/10'):
            po_transit_data()
        with wb.app.properties(status_bar='Extracting Data from DB 10/10'):
            po_data()
        with wb.app.properties(status_bar='Generating Forecast'):
            recalc(True)
        is_refresh=False
        print("Data Updated from DB")
        

    # Create a button to retrieve the selected values
    button = tk.Button(root, text="Load Selection", command=on_button_click)
    button.grid(row=3, column=0, pady=10)

    create_po_btn = tk.Button(root, text="Create PO", command=create_po)
    create_po_btn.grid(row=3, column=1, pady=10)

    recalculate = tk.Button(root, text="Recalculate", command=recalculate)
    recalculate.grid(row=3, column=2, pady=10)

    reset_btn = tk.Button(root, text="Refresh",command=refresh_start)
    reset_btn.grid(row=4, column=0, pady=10)

    get_data_btn = tk.Button(root, text="Get Data from Sheet",command=get_data_from_sheet)
    get_data_btn.grid(row=4, column=1, pady=10)

    update_woi_btn = tk.Button(root, text="Update WOI",command=update_woi)
    update_woi_btn.grid(row=4, column=2, pady=10)

    yscrollbar.grid(row=0,column=3,sticky='ns', rowspan=4)
    yscrollbar.config(command = lb.yview)
    root.mainloop()


# %%
if __name__ == "__main__":
    xl.Book("PO_Prioritization.xlsm").set_mock_caller()
    prioritization_filter()


# %%



