# Container Packing Optimization for 40HQ Container

This project optimizes the packing of items into a 40HQ shipping container using the py3dbp (3D Bin Packing) algorithm and provides interactive 3D visualization using Plotly.

## Features

- **3D Bin Packing Optimization**: Uses the py3dbp library to solve the container loading problem
- **40HQ Container Specifications**: Optimized for standard 40-foot High Cube containers
- **Interactive 3D Visualization**: Plotly-based visualization showing packed items in the container
- **Excel Integration**: Reads items from Excel and writes results back with detailed metrics
- **Comprehensive Metrics**: Volume utilization, weight utilization, and detailed packing statistics

## Container Specifications (40HQ)

- **Dimensions**: 12.032m × 2.352m × 2.698m
- **Volume**: 76.351 m³
- **Max Weight**: 26,580 kg

## Input Format

The Excel file should have a sheet named `items_to_pack` with the following columns:
- `name`: Item description
- `length`: Length in inches
- `width`: Width in inches  
- `height`: Height in inches
- `weight`: Weight in pounds

## Output

The optimization generates:

1. **Console Output**: Detailed packing results and metrics
2. **Excel Sheets**:
   - `Packing_Results`: Detailed position and dimensions of each packed item
   - `Summary_Statistics`: Key optimization metrics
   - `Item_Counts`: Number of each item type packed
3. **3D Visualization**: Interactive HTML file showing the packed container

## Usage

```python
python container_loading.py
```

## Results

For the sample item (Odelia Swivel Glider Rocker Recliner):
- **Maximum Items Packed**: 180 units
- **Volume Utilization**: 92.00%
- **Weight Utilization**: 6.14%
- **Total Packed Volume**: 70.247 m³
- **Total Packed Weight**: 1,632.96 kg

## Dependencies

- pandas
- py3dbp
- plotly
- numpy

## Files

- `container_loading.py`: Main optimization script
- `items_to_pack.xlsx`: Input data and results
- `container_packing_3d_visualization.html`: Interactive 3D visualization
