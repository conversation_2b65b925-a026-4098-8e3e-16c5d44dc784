#!/usr/bin/env python3
"""
Analyze why 2D views might show empty space despite 92% utilization
"""

import pandas as pd
import numpy as np

def analyze_2d_views():
    """Analyze the 2D view representation."""
    
    df = pd.read_excel('items_to_pack.xlsx', sheet_name='Packing_Results')
    
    print("="*80)
    print("2D VIEW ANALYSIS")
    print("="*80)
    
    # Container dimensions
    container_width = 2.352   # X-axis
    container_height = 2.698  # Y-axis  
    container_depth = 12.032  # Z-axis
    
    # Item dimensions
    item_width = df['actual_width_m'].iloc[0]   # 0.760m
    item_height = df['actual_height_m'].iloc[0] # 0.650m
    item_depth = df['actual_depth_m'].iloc[0]   # 0.790m
    
    print(f"Container dimensions: {container_width} × {container_height} × {container_depth} m")
    print(f"Item dimensions: {item_width} × {item_height} × {item_depth} m")
    
    # Analyze each view
    print(f"\n" + "="*50)
    print("TOP VIEW ANALYSIS (Width × Height)")
    print("="*50)
    
    # Top view should show the X-Y projection
    unique_x = sorted(df['position_x'].unique())
    unique_y = sorted(df['position_y'].unique())
    
    print(f"Unique X positions: {len(unique_x)} - {unique_x}")
    print(f"Unique Y positions: {len(unique_y)} - {unique_y}")
    
    # Calculate coverage
    x_coverage = (len(unique_x) * item_width) / container_width * 100
    y_coverage = (len(unique_y) * item_height) / container_height * 100
    
    print(f"X coverage: {len(unique_x)} items × {item_width:.3f}m = {len(unique_x) * item_width:.3f}m / {container_width:.3f}m = {x_coverage:.1f}%")
    print(f"Y coverage: {len(unique_y)} items × {item_height:.3f}m = {len(unique_y) * item_height:.3f}m / {container_height:.3f}m = {y_coverage:.1f}%")
    
    print(f"\n" + "="*50)
    print("FRONT VIEW ANALYSIS (Width × Depth)")
    print("="*50)
    
    # Front view shows X-Z projection
    unique_z = sorted(df['position_z'].unique())
    
    print(f"Unique X positions: {len(unique_x)} - spanning {unique_x[0]:.3f} to {unique_x[-1] + item_width:.3f}m")
    print(f"Unique Z positions: {len(unique_z)} - spanning {unique_z[0]:.3f} to {unique_z[-1] + item_depth:.3f}m")
    
    z_coverage = (len(unique_z) * item_depth) / container_depth * 100
    
    print(f"X coverage: {x_coverage:.1f}%")
    print(f"Z coverage: {len(unique_z)} items × {item_depth:.3f}m = {len(unique_z) * item_depth:.3f}m / {container_depth:.3f}m = {z_coverage:.1f}%")
    
    print(f"\n" + "="*50)
    print("SIDE VIEW ANALYSIS (Height × Depth)")
    print("="*50)
    
    print(f"Unique Y positions: {len(unique_y)} - spanning {unique_y[0]:.3f} to {unique_y[-1] + item_height:.3f}m")
    print(f"Unique Z positions: {len(unique_z)} - spanning {unique_z[0]:.3f} to {unique_z[-1] + item_depth:.3f}m")
    
    print(f"Y coverage: {y_coverage:.1f}%")
    print(f"Z coverage: {z_coverage:.1f}%")
    
    print(f"\n" + "="*50)
    print("OVERLAPPING ANALYSIS")
    print("="*50)
    
    # In 2D views, items at different Z levels will overlap in top view
    # Items at different Y levels will overlap in front view
    # Items at different X levels will overlap in side view
    
    print("Top view overlapping:")
    for x in unique_x:
        for y in unique_y:
            items_at_xy = len(df[(df['position_x'] == x) & (df['position_y'] == y)])
            if items_at_xy > 1:
                print(f"  Position ({x:.3f}, {y:.3f}): {items_at_xy} items stacked")
    
    print(f"\nFront view overlapping:")
    for x in unique_x:
        for z in unique_z[:5]:  # Show first 5 Z levels
            items_at_xz = len(df[(df['position_x'] == x) & (df['position_z'] == z)])
            if items_at_xz > 1:
                print(f"  Position ({x:.3f}, {z:.3f}): {items_at_xz} items stacked")
    
    print(f"\n" + "="*50)
    print("WHY 2D VIEWS MIGHT LOOK EMPTY")
    print("="*50)
    
    print("Possible reasons for apparent empty space:")
    print(f"1. Discrete packing: Items can't be split to fill partial spaces")
    print(f"2. Container utilization by dimension:")
    print(f"   - Width: {x_coverage:.1f}% ({len(unique_x)} × {item_width:.3f}m = {len(unique_x) * item_width:.3f}m)")
    print(f"   - Height: {y_coverage:.1f}% ({len(unique_y)} × {item_height:.3f}m = {len(unique_y) * item_height:.3f}m)")
    print(f"   - Depth: {z_coverage:.1f}% ({len(unique_z)} × {item_depth:.3f}m = {len(unique_z) * item_depth:.3f}m)")
    
    print(f"3. 2D projection effects:")
    print(f"   - Top view shows {len(unique_x)} × {len(unique_y)} = {len(unique_x) * len(unique_y)} positions")
    print(f"   - But {len(df)} items total, so {len(df) // (len(unique_x) * len(unique_y))} items per position")
    print(f"   - This creates overlapping rectangles that may appear as single items")
    
    print(f"\n4. Actual space efficiency:")
    total_item_volume = len(df) * item_width * item_height * item_depth
    container_volume = container_width * container_height * container_depth
    utilization = (total_item_volume / container_volume) * 100
    print(f"   - Volume utilization: {utilization:.2f}%")
    print(f"   - This is excellent for discrete item packing!")

if __name__ == "__main__":
    analyze_2d_views()
