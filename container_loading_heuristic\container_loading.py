import pandas as pd
from py3dbp import Packer, Bin, Item
from datetime import datetime
import plotly.graph_objects as go
import plotly.express as px
import numpy as np
import warnings
warnings.filterwarnings('ignore')

class ContainerPackingOptimizer:
    def __init__(self, excel_file_path):
        """
        Initialize the container packing optimizer.

        Args:
            excel_file_path (str): Path to the Excel file containing items to pack
        """
        self.excel_file_path = excel_file_path
        self.items_df = None
        self.packer = None
        self.container = None
        self.packed_items = []
        self.packing_results = {}

        # 40HQ Container specifications (in meters)
        self.container_specs = {
            'length': 12.032,  # 40 feet
            'width': 2.352,    # 7.7 feet
            'height': 2.698,   # 8.85 feet
            'max_weight': 26580,  # kg (typical max gross weight minus tare weight)
            'volume': 12.032 * 2.352 * 2.698  # cubic meters
        }

    def read_items_from_excel(self):
        """Read item data from the Excel file."""
        try:
            # Read the items data
            self.items_df = pd.read_excel(self.excel_file_path, sheet_name='items_to_pack')
            print(f"Successfully read {len(self.items_df)} item types from Excel file")
            print(f"Columns: {self.items_df.columns.tolist()}")
            print(f"Items preview:\n{self.items_df.head()}")
            return True

        except FileNotFoundError:
            print(f"Error: Excel file not found at {self.excel_file_path}")
            return False
        except Exception as e:
            print(f"Error reading Excel file: {e}")
            return False

    def validate_items(self):
        """Validate item dimensions and weights."""
        valid_items = []

        for index, row in self.items_df.iterrows():
            try:
                name = str(row['name'])
                length = float(row['length'])
                width = float(row['width'])
                height = float(row['height'])
                weight = float(row['weight'])

                # Convert inches to meters if needed (assuming input is in inches)
                length_m = length * 0.0254  # inches to meters
                width_m = width * 0.0254
                height_m = height * 0.0254
                weight_kg = weight * 0.453592  # pounds to kg (assuming weight is in lbs)

                # Validate dimensions
                if length_m <= 0 or width_m <= 0 or height_m <= 0 or weight_kg <= 0:
                    print(f"Warning: Skipping item '{name}' due to invalid dimensions or weight")
                    continue

                # Check if item fits in container
                if (length_m > self.container_specs['length'] or
                    width_m > self.container_specs['width'] or
                    height_m > self.container_specs['height']):
                    print(f"Warning: Item '{name}' is too large for container")
                    continue

                valid_items.append({
                    'name': name,
                    'length_m': length_m,
                    'width_m': width_m,
                    'height_m': height_m,
                    'weight_kg': weight_kg,
                    'volume_m3': length_m * width_m * height_m,
                    'original_length': length,
                    'original_width': width,
                    'original_height': height,
                    'original_weight': weight
                })

            except (ValueError, KeyError) as e:
                print(f"Error processing row {index}: {e}")
                continue

        print(f"Validated {len(valid_items)} items out of {len(self.items_df)}")
        return valid_items

    def optimize_packing(self, max_instances_per_item=1000):
        """
        Optimize the packing using py3dbp.

        Args:
            max_instances_per_item (int): Maximum instances of each item type to try
        """
        valid_items = self.validate_items()
        if not valid_items:
            print("No valid items to pack")
            return False

        # Initialize packer
        self.packer = Packer()

        # Create container (bin)
        self.container = Bin(
            name="40HQ_Container",
            width=self.container_specs['width'],
            height=self.container_specs['height'],
            depth=self.container_specs['length'],
            max_weight=self.container_specs['max_weight']
        )
        self.packer.add_bin(self.container)

        # Add items to packer
        item_instances = {}
        total_items_added = 0

        for item_data in valid_items:
            item_name = item_data['name']
            item_instances[item_name] = 0

            # Calculate theoretical maximum instances based on volume
            item_volume = item_data['volume_m3']
            theoretical_max = int(self.container_specs['volume'] / item_volume)
            max_to_try = min(max_instances_per_item, theoretical_max * 2)  # Try 2x theoretical max

            print(f"Adding up to {max_to_try} instances of '{item_name}'")

            for i in range(max_to_try):
                item = Item(
                    name=f"{item_name}_instance_{i+1}",
                    width=item_data['width_m'],
                    height=item_data['height_m'],
                    depth=item_data['length_m'],
                    weight=item_data['weight_kg']
                )
                self.packer.add_item(item)
                item_instances[item_name] += 1
                total_items_added += 1

        print(f"Total items added to packer: {total_items_added}")

        # Pack items
        print("Starting packing optimization...")
        self.packer.pack()

        # Analyze results
        self.analyze_packing_results(valid_items, item_instances)
        return True

    def analyze_packing_results(self, valid_items, item_instances):
        """Analyze the packing results and calculate metrics."""
        # Get packed items
        packed_items = self.container.items
        unpacked_items = self.container.unfitted_items

        print(f"\n=== PACKING RESULTS ===")
        print(f"Total items packed: {len(packed_items)}")
        print(f"Total items unpacked: {len(unpacked_items)}")

        # Calculate metrics
        total_packed_volume = float(sum(item.width * item.height * item.depth for item in packed_items))
        total_packed_weight = float(sum(item.weight for item in packed_items))

        volume_utilization = (total_packed_volume / self.container_specs['volume']) * 100
        weight_utilization = (total_packed_weight / self.container_specs['max_weight']) * 100

        # Count items by type
        item_counts = {}
        for item in packed_items:
            # Extract original item name (remove _instance_X suffix)
            original_name = '_'.join(item.name.split('_')[:-2])
            item_counts[original_name] = item_counts.get(original_name, 0) + 1

        # Store results
        self.packing_results = {
            'total_items_packed': len(packed_items),
            'total_items_unpacked': len(unpacked_items),
            'volume_utilization_percent': volume_utilization,
            'weight_utilization_percent': weight_utilization,
            'total_packed_volume_m3': total_packed_volume,
            'total_packed_weight_kg': total_packed_weight,
            'container_volume_m3': self.container_specs['volume'],
            'container_max_weight_kg': self.container_specs['max_weight'],
            'item_counts': item_counts,
            'packed_items_details': []
        }

        # Create detailed packed items list
        for item in packed_items:
            original_name = '_'.join(item.name.split('_')[:-2])

            # Get rotation information
            rotation_type = item.rotation_type
            rotation_description = self._get_rotation_description(rotation_type)

            # Get actual dimensions after rotation
            if hasattr(item, 'get_dimension'):
                rotated_dims = item.get_dimension()
                actual_width = float(rotated_dims[0])
                actual_height = float(rotated_dims[1])
                actual_depth = float(rotated_dims[2])
            else:
                actual_width = float(item.width)
                actual_height = float(item.height)
                actual_depth = float(item.depth)

            self.packing_results['packed_items_details'].append({
                'item_name': original_name,
                'instance_name': item.name,
                'position_x': float(item.position[0]),
                'position_y': float(item.position[1]),
                'position_z': float(item.position[2]),
                'original_width_m': float(item.width),
                'original_height_m': float(item.height),
                'original_depth_m': float(item.depth),
                'actual_width_m': actual_width,
                'actual_height_m': actual_height,
                'actual_depth_m': actual_depth,
                'rotation_type': rotation_type,
                'rotation_description': rotation_description,
                'weight_kg': float(item.weight),
                'volume_m3': float(item.width * item.height * item.depth)
            })

        # Print summary
        print(f"\n=== OPTIMIZATION METRICS ===")
        print(f"Volume Utilization: {volume_utilization:.2f}%")
        print(f"Weight Utilization: {weight_utilization:.2f}%")
        print(f"Total Packed Volume: {total_packed_volume:.3f} m³")
        print(f"Total Packed Weight: {total_packed_weight:.2f} kg")
        print(f"Container Volume: {self.container_specs['volume']:.3f} m³")
        print(f"Container Max Weight: {self.container_specs['max_weight']} kg")

        print(f"\n=== ITEMS PACKED BY TYPE ===")
        for item_name, count in item_counts.items():
            print(f"{item_name}: {count} units")

    def save_results_to_excel(self):
        """Save the packing results back to the Excel file."""
        try:
            # Read existing Excel file
            with pd.ExcelWriter(self.excel_file_path, mode='a', if_sheet_exists='replace') as writer:

                # Create packing results DataFrame
                if self.packing_results['packed_items_details']:
                    packed_df = pd.DataFrame(self.packing_results['packed_items_details'])
                    packed_df.to_excel(writer, sheet_name='Packing_Results', index=False)

                # Create summary statistics DataFrame
                summary_data = {
                    'Metric': [
                        'Total Items Packed',
                        'Total Items Unpacked',
                        'Volume Utilization (%)',
                        'Weight Utilization (%)',
                        'Total Packed Volume (m³)',
                        'Total Packed Weight (kg)',
                        'Container Volume (m³)',
                        'Container Max Weight (kg)',
                        'Optimization Timestamp'
                    ],
                    'Value': [
                        self.packing_results['total_items_packed'],
                        self.packing_results['total_items_unpacked'],
                        f"{self.packing_results['volume_utilization_percent']:.2f}",
                        f"{self.packing_results['weight_utilization_percent']:.2f}",
                        f"{self.packing_results['total_packed_volume_m3']:.3f}",
                        f"{self.packing_results['total_packed_weight_kg']:.2f}",
                        f"{self.packing_results['container_volume_m3']:.3f}",
                        self.packing_results['container_max_weight_kg'],
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    ]
                }

                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary_Statistics', index=False)

                # Create item counts summary
                if self.packing_results['item_counts']:
                    item_counts_data = {
                        'Item_Name': list(self.packing_results['item_counts'].keys()),
                        'Quantity_Packed': list(self.packing_results['item_counts'].values())
                    }
                    item_counts_df = pd.DataFrame(item_counts_data)
                    item_counts_df.to_excel(writer, sheet_name='Item_Counts', index=False)

            print(f"\nResults saved to {self.excel_file_path}")
            return True

        except Exception as e:
            print(f"Error saving results to Excel: {e}")
            return False

    def print_detailed_results(self):
        """Print comprehensive results to console."""
        if not self.packing_results:
            print("No packing results available")
            return

        print("\n" + "="*80)
        print("CONTAINER PACKING OPTIMIZATION RESULTS")
        print("="*80)

        print(f"\nCONTAINER SPECIFICATIONS (40HQ):")
        print(f"  Dimensions: {self.container_specs['length']:.3f}m x {self.container_specs['width']:.3f}m x {self.container_specs['height']:.3f}m")
        print(f"  Volume: {self.container_specs['volume']:.3f} m³")
        print(f"  Max Weight: {self.container_specs['max_weight']:,} kg")

        print(f"\nPACKING SUMMARY:")
        print(f"  Items Successfully Packed: {self.packing_results['total_items_packed']}")
        print(f"  Items Not Packed: {self.packing_results['total_items_unpacked']}")
        print(f"  Volume Utilization: {self.packing_results['volume_utilization_percent']:.2f}%")
        print(f"  Weight Utilization: {self.packing_results['weight_utilization_percent']:.2f}%")
        print(f"  Total Packed Volume: {self.packing_results['total_packed_volume_m3']:.3f} m³")
        print(f"  Total Packed Weight: {self.packing_results['total_packed_weight_kg']:.2f} kg")

        print(f"\nITEMS PACKED BY TYPE:")
        for item_name, count in self.packing_results['item_counts'].items():
            print(f"  {item_name}: {count} units")

        print("\n" + "="*80)

    def _get_rotation_description(self, rotation_type):
        """
        Get human-readable description of rotation type.

        Args:
            rotation_type (int): The rotation type from py3dbp

        Returns:
            str: Description of the rotation
        """
        rotation_descriptions = {
            0: "No rotation (W×H×D)",
            1: "90° Z-axis rotation (H×W×D)",
            2: "90° Y-axis rotation (D×H×W)",
            3: "90° X-axis rotation (W×D×H)",
            4: "Z+Y rotation (H×D×W)",
            5: "Z+X rotation (D×W×H)"
        }
        return rotation_descriptions.get(rotation_type, f"Unknown rotation type {rotation_type}")

    def create_3d_visualization(self, save_html=True, show_plot=True, max_items_to_show=None):
        """
        Create a 3D visualization of the packed items using Plotly.

        Args:
            save_html (bool): Whether to save the plot as HTML file
            show_plot (bool): Whether to display the plot
            max_items_to_show (int): Maximum number of items to show, None for all items
        """
        if not self.packing_results or not self.packing_results['packed_items_details']:
            print("No packing results available for visualization")
            return None

        # Create figure with subplots for different views
        from plotly.subplots import make_subplots

        # Create main 3D plot
        fig = go.Figure()

        # Add container outline - correct coordinate mapping
        # Based on py3dbp: X=width, Y=height, Z=depth
        container_x = self.container_specs['width']   # 2.352m
        container_y = self.container_specs['height']  # 2.698m
        container_z = self.container_specs['length']  # 12.032m

        # Container wireframe - make it more prominent
        container_vertices = [
            [0, 0, 0], [container_x, 0, 0], [container_x, container_y, 0], [0, container_y, 0],  # bottom
            [0, 0, container_z], [container_x, 0, container_z], [container_x, container_y, container_z], [0, container_y, container_z]  # top
        ]

        # Container edges
        container_edges = [
            [0, 1], [1, 2], [2, 3], [3, 0],  # bottom edges
            [4, 5], [5, 6], [6, 7], [7, 4],  # top edges
            [0, 4], [1, 5], [2, 6], [3, 7]   # vertical edges
        ]

        # Add container wireframe with thicker lines
        for i, edge in enumerate(container_edges):
            start, end = edge
            fig.add_trace(go.Scatter3d(
                x=[container_vertices[start][0], container_vertices[end][0]],
                y=[container_vertices[start][1], container_vertices[end][1]],
                z=[container_vertices[start][2], container_vertices[end][2]],
                mode='lines',
                line=dict(color='red', width=6),
                name='Container Outline' if i == 0 else '',
                showlegend=True if i == 0 else False,
                hoverinfo='skip'
            ))

        # Show all items or limit based on parameter
        total_items = len(self.packing_results['packed_items_details'])
        if max_items_to_show is None:
            items_to_show = self.packing_results['packed_items_details']
            print(f"Showing all {total_items} items with wireframes")
        else:
            items_to_show = self.packing_results['packed_items_details'][:max_items_to_show]
            print(f"Showing {len(items_to_show)} out of {total_items} items")

        # Create a more efficient visualization using scatter3d with custom markers
        x_positions = []
        y_positions = []
        z_positions = []
        hover_texts = []
        colors = []
        sizes = []

        # Use a color scale for better distinction
        import random
        random.seed(42)  # For consistent colors

        for i, item in enumerate(items_to_show):
            x_pos = item['position_x']
            y_pos = item['position_y']
            z_pos = item['position_z']
            width = item['actual_width_m']
            height = item['actual_height_m']
            depth = item['actual_depth_m']

            # Add center point of each box
            x_positions.append(x_pos + width/2)
            y_positions.append(y_pos + height/2)
            z_positions.append(z_pos + depth/2)

            # Create hover text
            hover_text = (f"<b>Item #{i+1}</b><br>" +
                         f"Position: ({x_pos:.2f}, {y_pos:.2f}, {z_pos:.2f})<br>" +
                         f"Dimensions: {width:.2f} × {height:.2f} × {depth:.2f} m<br>" +
                         f"Rotation: {item['rotation_description']}<br>" +
                         f"Weight: {item['weight_kg']:.2f} kg<br>" +
                         f"Volume: {item['volume_m3']:.3f} m³")
            hover_texts.append(hover_text)

            # Color based on position for better distinction
            colors.append(i)

            # Size based on volume
            volume = width * height * depth
            sizes.append(max(5, min(20, volume * 50)))  # Scale size appropriately

        # Add scatter plot for item centers
        fig.add_trace(go.Scatter3d(
            x=x_positions,
            y=y_positions,
            z=z_positions,
            mode='markers',
            marker=dict(
                size=sizes,
                color=colors,
                colorscale='Viridis',
                opacity=0.8,
                line=dict(width=2, color='black')
            ),
            text=hover_texts,
            hovertemplate='%{text}<extra></extra>',
            name=f'Packed Items (showing {len(items_to_show)})'
        ))

        # Add wireframe boxes for all items to show actual placement
        print(f"Creating wireframes for all {len(items_to_show)} items...")
        for i, item in enumerate(items_to_show):
            x_pos = item['position_x']
            y_pos = item['position_y']
            z_pos = item['position_z']
            width = item['actual_width_m']
            height = item['actual_height_m']
            depth = item['actual_depth_m']

            # Create wireframe box with all 12 edges
            # Bottom face edges
            bottom_edges = [
                ([x_pos, x_pos+width], [y_pos, y_pos], [z_pos, z_pos]),  # front bottom
                ([x_pos+width, x_pos+width], [y_pos, y_pos+height], [z_pos, z_pos]),  # right bottom
                ([x_pos+width, x_pos], [y_pos+height, y_pos+height], [z_pos, z_pos]),  # back bottom
                ([x_pos, x_pos], [y_pos+height, y_pos], [z_pos, z_pos])  # left bottom
            ]

            # Top face edges
            top_edges = [
                ([x_pos, x_pos+width], [y_pos, y_pos], [z_pos+depth, z_pos+depth]),  # front top
                ([x_pos+width, x_pos+width], [y_pos, y_pos+height], [z_pos+depth, z_pos+depth]),  # right top
                ([x_pos+width, x_pos], [y_pos+height, y_pos+height], [z_pos+depth, z_pos+depth]),  # back top
                ([x_pos, x_pos], [y_pos+height, y_pos], [z_pos+depth, z_pos+depth])  # left top
            ]

            # Vertical edges
            vertical_edges = [
                ([x_pos, x_pos], [y_pos, y_pos], [z_pos, z_pos+depth]),  # front left
                ([x_pos+width, x_pos+width], [y_pos, y_pos], [z_pos, z_pos+depth]),  # front right
                ([x_pos+width, x_pos+width], [y_pos+height, y_pos+height], [z_pos, z_pos+depth]),  # back right
                ([x_pos, x_pos], [y_pos+height, y_pos+height], [z_pos, z_pos+depth])  # back left
            ]

            all_edges = bottom_edges + top_edges + vertical_edges

            # Use different colors for better distinction
            color_cycle = [
                'rgba(255, 0, 0, 0.8)',    # Red
                'rgba(0, 255, 0, 0.8)',    # Green
                'rgba(0, 0, 255, 0.8)',    # Blue
                'rgba(255, 255, 0, 0.8)',  # Yellow
                'rgba(255, 0, 255, 0.8)',  # Magenta
                'rgba(0, 255, 255, 0.8)',  # Cyan
                'rgba(255, 128, 0, 0.8)',  # Orange
                'rgba(128, 0, 255, 0.8)',  # Purple
                'rgba(255, 192, 203, 0.8)', # Pink
                'rgba(0, 128, 0, 0.8)'     # Dark Green
            ]

            item_color = color_cycle[i % len(color_cycle)]

            # Combine all edges into single trace per item for better performance
            all_x = []
            all_y = []
            all_z = []

            for edge_x, edge_y, edge_z in all_edges:
                all_x.extend(edge_x + [None])  # None creates line breaks
                all_y.extend(edge_y + [None])
                all_z.extend(edge_z + [None])

            fig.add_trace(go.Scatter3d(
                x=all_x,
                y=all_y,
                z=all_z,
                mode='lines',
                line=dict(color=item_color, width=2),
                name=f'Item {i+1}' if i < 10 else '',
                showlegend=True if i < 10 else False,
                hovertemplate=f"<b>Item #{i+1}</b><br>" +
                            f"Position: ({x_pos:.2f}, {y_pos:.2f}, {z_pos:.2f})<br>" +
                            f"Dimensions: {width:.2f} × {height:.2f} × {depth:.2f} m<br>" +
                            f"Rotation: {item['rotation_description']}<extra></extra>"
            ))

            # Progress indicator for large numbers of items
            if (i + 1) % 50 == 0:
                print(f"  Created wireframes for {i + 1} items...")

        # Calculate actual space utilization for better visualization
        if items_to_show:
            max_x_used = max(item['position_x'] + item['actual_width_m'] for item in items_to_show)
            max_y_used = max(item['position_y'] + item['actual_height_m'] for item in items_to_show)
            max_z_used = max(item['position_z'] + item['actual_depth_m'] for item in items_to_show)

            # Add small margin for better visualization
            margin = 0.1
            x_range = [0, max(max_x_used + margin, container_x)]
            y_range = [0, max(max_y_used + margin, container_y)]
            z_range = [0, max(max_z_used + margin, container_z)]
        else:
            x_range = [0, container_x]
            y_range = [0, container_y]
            z_range = [0, container_z]

        # Update layout with better styling and correct proportions
        fig.update_layout(
            title=dict(
                text=f"3D Container Packing Visualization - 40HQ Container<br>" +
                     f"<sub>Showing {len(items_to_show)} of {total_items} items | " +
                     f"Volume Utilization: {self.packing_results['volume_utilization_percent']:.1f}% | " +
                     f"Space Used: X={max_x_used:.1f}m/{container_x:.1f}m, Y={max_y_used:.1f}m/{container_y:.1f}m, Z={max_z_used:.1f}m/{container_z:.1f}m</sub>",
                x=0.5,
                font=dict(size=16)
            ),
            scene=dict(
                xaxis_title="Width (m) - Container Dimension: 2.352m",
                yaxis_title="Height (m) - Container Dimension: 2.698m",
                zaxis_title="Depth/Length (m) - Container Dimension: 12.032m",
                xaxis=dict(
                    range=x_range,
                    showgrid=True,
                    gridcolor='lightgray',
                    gridwidth=1,
                    dtick=0.5
                ),
                yaxis=dict(
                    range=y_range,
                    showgrid=True,
                    gridcolor='lightgray',
                    gridwidth=1,
                    dtick=0.5
                ),
                zaxis=dict(
                    range=z_range,
                    showgrid=True,
                    gridcolor='lightgray',
                    gridwidth=1,
                    dtick=1.0
                ),
                aspectmode='data',
                bgcolor='white',
                camera=dict(
                    eye=dict(x=1.2, y=1.2, z=0.8)
                )
            ),
            width=1400,
            height=1000,
            margin=dict(l=0, r=0, t=100, b=0),
            showlegend=True,
            legend=dict(
                x=0.02,
                y=0.98,
                bgcolor='rgba(255,255,255,0.9)',
                bordercolor='black',
                borderwidth=1
            )
        )

        # Add annotations with key statistics
        if items_to_show:
            x_util = (max_x_used / container_x) * 100
            y_util = (max_y_used / container_y) * 100
            z_util = (max_z_used / container_z) * 100

            fig.add_annotation(
                x=0.02, y=0.02,
                xref='paper', yref='paper',
                text=f"Container: {container_x:.1f}m × {container_y:.1f}m × {container_z:.1f}m<br>" +
                     f"Space Used: {max_x_used:.1f}m × {max_y_used:.1f}m × {max_z_used:.1f}m<br>" +
                     f"Utilization: X={x_util:.1f}%, Y={y_util:.1f}%, Z={z_util:.1f}%<br>" +
                     f"Volume: {self.packing_results['total_packed_volume_m3']:.1f}/{self.container_specs['volume']:.1f} m³ ({self.packing_results['volume_utilization_percent']:.1f}%)<br>" +
                     f"Items Packed: {total_items}",
                showarrow=False,
                bgcolor='rgba(255,255,255,0.9)',
                bordercolor='black',
                borderwidth=1,
                font=dict(size=11)
            )

        # Save as HTML if requested
        if save_html:
            html_filename = "container_packing_3d_visualization.html"
            fig.write_html(html_filename)
            print(f"3D visualization saved as: {html_filename}")

        # Show plot if requested
        if show_plot:
            fig.show()

        return fig

    def create_2d_cross_sections(self, save_html=True):
        """Create 2D cross-section views of the packing."""
        from plotly.subplots import make_subplots

        # Create subplots for different views
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Top View (Width×Height)', 'Front View (Width×Depth)', 'Side View (Height×Depth)', 'Packing Statistics'),
            specs=[[{'type': 'scatter'}, {'type': 'scatter'}],
                   [{'type': 'scatter'}, {'type': 'table'}]]
        )

        items = self.packing_results['packed_items_details']

        # Top view (X-Y plane)
        for i, item in enumerate(items[:100]):  # Limit for clarity
            x_pos = item['position_x']
            y_pos = item['position_y']
            width = item['actual_width_m']
            height = item['actual_height_m']

            # Draw rectangle
            fig.add_shape(
                type="rect",
                x0=x_pos, y0=y_pos,
                x1=x_pos + width, y1=y_pos + height,
                line=dict(color="blue", width=1),
                fillcolor="lightblue",
                opacity=0.5,
                row=1, col=1
            )

        # Front view (X-Z plane)
        for i, item in enumerate(items[:100]):
            x_pos = item['position_x']
            z_pos = item['position_z']
            width = item['actual_width_m']
            depth = item['actual_depth_m']

            fig.add_shape(
                type="rect",
                x0=x_pos, y0=z_pos,
                x1=x_pos + width, y1=z_pos + depth,
                line=dict(color="green", width=1),
                fillcolor="lightgreen",
                opacity=0.5,
                row=1, col=2
            )

        # Side view (Y-Z plane)
        for i, item in enumerate(items[:100]):
            y_pos = item['position_y']
            z_pos = item['position_z']
            height = item['actual_height_m']
            depth = item['actual_depth_m']

            fig.add_shape(
                type="rect",
                x0=y_pos, y0=z_pos,
                x1=y_pos + height, y1=z_pos + depth,
                line=dict(color="red", width=1),
                fillcolor="lightcoral",
                opacity=0.5,
                row=2, col=1
            )

        # Add container outlines - correct coordinate mapping
        # X=width, Y=height, Z=depth
        container_x = self.container_specs['width']   # 2.352m
        container_y = self.container_specs['height']  # 2.698m
        container_z = self.container_specs['length']  # 12.032m

        # Container outline for each view
        # Top view (X-Y): width x height
        # Front view (X-Z): width x depth
        # Side view (Y-Z): height x depth
        for row, col, x_max, y_max in [(1, 1, container_x, container_y),
                                       (1, 2, container_x, container_z),
                                       (2, 1, container_y, container_z)]:
            fig.add_shape(
                type="rect",
                x0=0, y0=0, x1=x_max, y1=y_max,
                line=dict(color="black", width=3),
                fillcolor="rgba(0,0,0,0)",
                row=row, col=col
            )

        # Add statistics table
        stats_data = [
            ['Total Items Packed', self.packing_results['total_items_packed']],
            ['Volume Utilization', f"{self.packing_results['volume_utilization_percent']:.1f}%"],
            ['Weight Utilization', f"{self.packing_results['weight_utilization_percent']:.1f}%"],
            ['Container Volume', f"{self.container_specs['volume']:.1f} m³"],
            ['Used Volume', f"{self.packing_results['total_packed_volume_m3']:.1f} m³"],
            ['Total Weight', f"{self.packing_results['total_packed_weight_kg']:.1f} kg"]
        ]

        fig.add_trace(go.Table(
            header=dict(values=['Metric', 'Value'],
                       fill_color='lightblue',
                       align='left'),
            cells=dict(values=[[row[0] for row in stats_data],
                              [row[1] for row in stats_data]],
                      fill_color='white',
                      align='left')
        ), row=2, col=2)

        # Update layout
        fig.update_layout(
            title_text="Container Packing - 2D Cross-Section Views",
            showlegend=False,
            height=800,
            width=1200
        )

        # Update axes with correct labels
        fig.update_xaxes(title_text="Width (m)", row=1, col=1)
        fig.update_yaxes(title_text="Height (m)", row=1, col=1)
        fig.update_xaxes(title_text="Width (m)", row=1, col=2)
        fig.update_yaxes(title_text="Depth (m)", row=1, col=2)
        fig.update_xaxes(title_text="Height (m)", row=2, col=1)
        fig.update_yaxes(title_text="Depth (m)", row=2, col=1)

        if save_html:
            html_filename = "container_packing_2d_views.html"
            fig.write_html(html_filename)
            print(f"2D cross-section views saved as: {html_filename}")

        return fig

def main():
    """Main function to run the container packing optimization."""
    excel_file = "items_to_pack.xlsx"

    print("="*80)
    print("CONTAINER PACKING OPTIMIZATION - 40HQ CONTAINER")
    print("="*80)

    # Initialize optimizer
    optimizer = ContainerPackingOptimizer(excel_file)

    # Read items from Excel
    if not optimizer.read_items_from_excel():
        print("Failed to read items from Excel file")
        return

    # Run optimization
    if not optimizer.optimize_packing(max_instances_per_item=1000):
        print("Optimization failed")
        return

    # Print detailed results
    optimizer.print_detailed_results()

    # Save results to Excel
    optimizer.save_results_to_excel()

    # Create visualizations
    print("\nCreating complete 3D visualization with all items...")
    optimizer.create_3d_visualization(save_html=True, show_plot=True, max_items_to_show=None)

    print("Creating 2D cross-section views...")
    optimizer.create_2d_cross_sections(save_html=True)

    print(f"\nOptimization completed successfully!")
    print(f"Maximum items that can be packed: {optimizer.packing_results['total_items_packed']}")
    print(f"Volume utilization: {optimizer.packing_results['volume_utilization_percent']:.2f}%")
    print(f"Weight utilization: {optimizer.packing_results['weight_utilization_percent']:.2f}%")

if __name__ == "__main__":
    main()