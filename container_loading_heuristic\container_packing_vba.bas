Sub RunContainerPackingOptimization()
    '
    ' Simplified Container Packing Optimization
    ' Uses WScript.Shell to run Python script and update results
    '
    ' REQUIRED VBA REFERENCES:
    ' - Microsoft Scripting Runtime (for WScript.Shell)
    '

    Dim shell As Object
    Dim workbookPath As String
    Dim command As String
    Dim pythonScript As String

    ' Create WScript.Shell object
    Set shell = CreateObject("WScript.Shell")

    ' Get current workbook path
    workbookPath = ThisWorkbook.Path
    pythonScript = workbookPath & "\container_loading.py"

    ' Check if Python script exists
    If Dir(pythonScript) = "" Then
        MsgBox "Python script not found: " & pythonScript, vbCritical, "Error"
        Exit Sub
    End If

    ' Save workbook
    ThisWorkbook.Save

    ' Show status
    Application.StatusBar = "Running container packing optimization..."
    Application.ScreenUpdating = False

    ' Build and run command
    command = "cmd /c cd /d """ & workbookPath & """ && python container_loading.py"

    ' Run script and wait for completion
    shell.Run command, 0, True  ' 0 = hidden window, True = wait for completion

    ' Refresh all sheets
    Call RefreshAllSheets

    ' Show completion message
    Application.StatusBar = False
    Application.ScreenUpdating = True
    MsgBox "Container packing optimization completed!" & vbCrLf & _
           "Results have been updated in the Excel sheets.", vbInformation, "Optimization Complete"

    ' Clean up
    Set shell = Nothing

End Sub

Sub RefreshAllSheets()
    '
    ' Refresh all sheets and auto-fit columns
    '

    Dim ws As Worksheet
    Dim resultSheets As Variant
    Dim i As Integer

    ' Result sheets to refresh
    resultSheets = Array("Packing_Results", "Summary_Statistics", "Item_Counts")

    ' Refresh each sheet if it exists
    For i = 0 To UBound(resultSheets)
        On Error Resume Next
        Set ws = ThisWorkbook.Sheets(resultSheets(i))
        If Not ws Is Nothing Then
            ws.Calculate
            ws.Columns.AutoFit
            ws.Rows.AutoFit
        End If
        Set ws = Nothing
        On Error GoTo 0
    Next i

End Sub

Sub OpenVisualizations()
    '
    ' Open generated HTML visualization files
    '

    Dim shell As Object
    Dim workbookPath As String

    Set shell = CreateObject("WScript.Shell")
    workbookPath = ThisWorkbook.Path

    ' Open 3D visualization
    If Dir(workbookPath & "\container_packing_3d_visualization.html") <> "" Then
        shell.Run """" & workbookPath & "\container_packing_3d_visualization.html""", 1
    End If

    ' Open 2D visualization
    If Dir(workbookPath & "\container_packing_2d_views.html") <> "" Then
        shell.Run """" & workbookPath & "\container_packing_2d_views.html""", 1
    End If

    Set shell = Nothing

End Sub
