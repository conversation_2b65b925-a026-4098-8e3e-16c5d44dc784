Sub RunContainerPackingOptimization()
    '
    ' Container Packing Optimization VBA Trigger
    ' This subroutine runs the Python container packing script
    ' Attach this to a command button in Excel
    '
    ' REQUIRED VBA REFERENCES:
    ' - Microsoft Excel Object Library (usually enabled by default)
    ' - Microsoft Office Object Library (usually enabled by default)
    ' - Microsoft Scripting Runtime (for FileSystemObject - optional)
    '
    ' To enable references:
    ' 1. In VBA Editor, go to Tools → References
    ' 2. Check the boxes for required libraries
    '
    
    Dim pythonScriptPath As String
    Dim workbookPath As String
    Dim command As String
    Dim result As Integer
    
    ' Disable screen updating and alerts for better performance
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    
    ' Get the current workbook path and directory
    workbookPath = ThisWorkbook.Path
    pythonScriptPath = workbookPath & "\container_loading.py"
    
    ' Check if Python script exists
    If Dir(pythonScriptPath) = "" Then
        MsgBox "Error: Python script 'container_loading.py' not found in the same directory as this Excel file." & vbCrLf & _
               "Expected location: " & pythonScriptPath, vbCritical, "Script Not Found"
        GoTo Cleanup
    End If
    
    ' Save the current workbook before running optimization
    ThisWorkbook.Save
    
    ' Show progress message
    Application.StatusBar = "Running container packing optimization..."
    
    ' Build the command to run Python script
    ' Change directory to workbook location and run the script
    command = "cmd /c ""cd /d """ & workbookPath & """ && python container_loading.py"""
    
    ' Inform user that optimization is starting
    MsgBox "Container packing optimization is starting..." & vbCrLf & vbCrLf & _
           "This may take a few minutes. Click OK to continue." & vbCrLf & _
           "The script will:" & vbCrLf & _
           "• Read items from the current Excel file" & vbCrLf & _
           "• Optimize packing using py3dbp" & vbCrLf & _
           "• Update Excel with results" & vbCrLf & _
           "• Generate 3D and 2D visualizations", vbInformation, "Starting Optimization"
    
    ' Run the Python script
    result = Shell(command, vbNormalFocus)
    
    ' Wait a moment for the script to start
    Application.Wait (Now + TimeValue("0:00:02"))
    
    ' Inform user about completion
    MsgBox "Optimization script has been launched!" & vbCrLf & vbCrLf & _
           "The script is now running and will:" & vbCrLf & _
           "• Update this Excel file with packing results" & vbCrLf & _
           "• Create new sheets with detailed data" & vbCrLf & _
           "• Generate HTML visualization files" & vbCrLf & vbCrLf & _
           "You can continue working while the script runs.", vbInformation, "Optimization Launched"

Cleanup:
    ' Restore Excel settings
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    Application.StatusBar = False
    
End Sub

Sub RefreshPackingResults()
    '
    ' Refresh Packing Results
    ' This subroutine refreshes the data in the packing results sheets
    ' Useful after the Python script has completed
    '
    
    Dim ws As Worksheet
    Dim sheetsToRefresh As Variant
    Dim i As Integer
    
    ' List of sheets that contain packing results
    sheetsToRefresh = Array("Packing_Results", "Summary_Statistics", "Item_Counts")
    
    Application.ScreenUpdating = False
    
    ' Refresh each results sheet if it exists
    For i = 0 To UBound(sheetsToRefresh)
        On Error Resume Next
        Set ws = ThisWorkbook.Sheets(sheetsToRefresh(i))
        If Not ws Is Nothing Then
            ws.Calculate
            ws.Columns.AutoFit
            Set ws = Nothing
        End If
        On Error GoTo 0
    Next i
    
    Application.ScreenUpdating = True
    
    MsgBox "Packing results sheets have been refreshed and auto-fitted.", vbInformation, "Results Refreshed"
    
End Sub

Sub OpenVisualizationFiles()
    '
    ' Open Visualization Files
    ' This subroutine opens the generated HTML visualization files
    '
    
    Dim workbookPath As String
    Dim file3D As String
    Dim file2D As String
    
    workbookPath = ThisWorkbook.Path
    file3D = workbookPath & "\container_packing_3d_visualization.html"
    file2D = workbookPath & "\container_packing_2d_views.html"
    
    ' Check and open 3D visualization
    If Dir(file3D) <> "" Then
        Shell "rundll32 url.dll,FileProtocolHandler " & file3D, vbNormalFocus
    Else
        MsgBox "3D visualization file not found: " & file3D, vbExclamation, "File Not Found"
    End If
    
    ' Wait a moment before opening second file
    Application.Wait (Now + TimeValue("0:00:01"))
    
    ' Check and open 2D visualization
    If Dir(file2D) <> "" Then
        Shell "rundll32 url.dll,FileProtocolHandler " & file2D, vbNormalFocus
    Else
        MsgBox "2D visualization file not found: " & file2D, vbExclamation, "File Not Found"
    End If
    
End Sub

Sub CheckPythonDependencies()
    '
    ' Check Python Dependencies
    ' This subroutine checks if required Python packages are installed
    '
    
    Dim command As String
    Dim result As String
    
    ' Check if Python is available
    command = "cmd /c ""python --version 2>&1"""
    
    MsgBox "Checking Python dependencies..." & vbCrLf & vbCrLf & _
           "Required packages:" & vbCrLf & _
           "• xlwings" & vbCrLf & _
           "• py3dbp" & vbCrLf & _
           "• plotly" & vbCrLf & vbCrLf & _
           "Check the console window for results.", vbInformation, "Dependency Check"
    
    ' Run dependency check
    command = "cmd /c ""python -c ""import xlwings, py3dbp, plotly; print('All required packages are installed successfully!')"""" & pause"""
    Shell command, vbNormalFocus
    
End Sub
