Sub RunContainerPackingOptimization()
    '
    ' Simplified Container Packing Optimization
    ' Uses WScript.Shell to run Python script without waiting
    '

    Dim shell As Object
    Dim workbookPath As String
    Dim command As String
    Dim pythonScript As String

    ' Create WScript.Shell object
    Set shell = CreateObject("WScript.Shell")

    ' Get current workbook path
    workbookPath = ThisWorkbook.Path
    pythonScript = workbookPath & "\container_loading.py"

    ' Check if Python script exists
    If Dir(pythonScript) = "" Then
        MsgBox "Python script not found: " & pythonScript, vbCritical, "Error"
        Exit Sub
    End If

    ' Save workbook and close to avoid conflicts
    ThisWorkbook.Save

    ' Show message before running
    MsgBox "Starting container packing optimization..." & vbCrLf & vbCrLf & _
           "The Python script will:" & vbCrLf & _
           "• Read data from this Excel file" & vbCrLf & _
           "• Run the optimization" & vbCrLf & _
           "• Update the Excel file with results" & vbCrLf & _
           "• Keep Excel open when complete" & vbCrLf & vbCrLf & _
           "Click OK to start the process.", vbInformation, "Starting Optimization"

    ' Build command to run Python script
    command = "cmd /c cd /d """ & workbookPath & """ && python container_loading.py"

    ' Run script without waiting (to avoid Excel conflicts)
    shell.Run command, 1, False  ' 1 = normal window, False = don't wait

    ' Show completion message
    MsgBox "Optimization script has been started!" & vbCrLf & vbCrLf & _
           "The script is now running in the background." & vbCrLf & _
           "Excel will be updated automatically when complete." & vbCrLf & vbCrLf & _
           "You can continue working or close this message.", vbInformation, "Script Started"

    ' Clean up
    Set shell = Nothing

End Sub

Sub RefreshResults()
    '
    ' Refresh result sheets after optimization completes
    ' Call this manually after the Python script finishes
    '

    Dim ws As Worksheet
    Dim resultSheets As Variant
    Dim i As Integer
    Dim sheetsFound As Integer

    ' Result sheets to refresh
    resultSheets = Array("Packing_Results", "Summary_Statistics", "Item_Counts")
    sheetsFound = 0

    Application.ScreenUpdating = False

    ' Refresh each sheet if it exists
    For i = 0 To UBound(resultSheets)
        On Error Resume Next
        Set ws = ThisWorkbook.Sheets(resultSheets(i))
        If Not ws Is Nothing Then
            ws.Calculate
            ws.Columns.AutoFit
            ws.Rows.AutoFit
            sheetsFound = sheetsFound + 1
        End If
        Set ws = Nothing
        On Error GoTo 0
    Next i

    Application.ScreenUpdating = True

    If sheetsFound > 0 Then
        MsgBox "Refreshed " & sheetsFound & " result sheets successfully!", vbInformation, "Results Refreshed"
    Else
        MsgBox "No result sheets found. Run the optimization first.", vbExclamation, "No Results"
    End If

End Sub

Sub OpenVisualizations()
    '
    ' Open generated HTML visualization files
    '

    Dim shell As Object
    Dim workbookPath As String

    Set shell = CreateObject("WScript.Shell")
    workbookPath = ThisWorkbook.Path

    ' Open 3D visualization
    If Dir(workbookPath & "\container_packing_3d_visualization.html") <> "" Then
        shell.Run """" & workbookPath & "\container_packing_3d_visualization.html""", 1
    End If

    ' Open 2D visualization
    If Dir(workbookPath & "\container_packing_2d_views.html") <> "" Then
        shell.Run """" & workbookPath & "\container_packing_2d_views.html""", 1
    End If

    Set shell = Nothing

End Sub
