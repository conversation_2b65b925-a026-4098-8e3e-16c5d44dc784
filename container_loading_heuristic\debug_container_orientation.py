#!/usr/bin/env python3
"""
Debug container orientation and coordinate mapping
"""

from py3dbp import Packer, Bin, Item
import pandas as pd

def debug_container_orientation():
    """Debug the container orientation issue."""
    
    print("="*80)
    print("DEBUGGING CONTAINER ORIENTATION")
    print("="*80)
    
    # Read our actual data
    df = pd.read_excel('items_to_pack.xlsx', sheet_name='Packing_Results')
    
    print("Current data analysis:")
    print(f"Items packed: {len(df)}")
    print(f"Position ranges:")
    print(f"  X: {df['position_x'].min():.3f} to {df['position_x'].max():.3f}")
    print(f"  Y: {df['position_y'].min():.3f} to {df['position_y'].max():.3f}")
    print(f"  Z: {df['position_z'].min():.3f} to {df['position_z'].max():.3f}")
    
    # Item dimensions
    item_w = df['actual_width_m'].iloc[0]
    item_h = df['actual_height_m'].iloc[0]
    item_d = df['actual_depth_m'].iloc[0]
    
    print(f"\nItem dimensions:")
    print(f"  Width: {item_w:.3f}m")
    print(f"  Height: {item_h:.3f}m")
    print(f"  Depth: {item_d:.3f}m")
    
    # Calculate extents
    max_x = df['position_x'].max() + item_w
    max_y = df['position_y'].max() + item_h
    max_z = df['position_z'].max() + item_d
    
    print(f"\nActual space used:")
    print(f"  X extent: {max_x:.3f}m")
    print(f"  Y extent: {max_y:.3f}m")
    print(f"  Z extent: {max_z:.3f}m")
    
    # Container specs from our code
    container_length = 12.032  # depth in py3dbp
    container_width = 2.352    # width in py3dbp
    container_height = 2.698   # height in py3dbp
    
    print(f"\nContainer dimensions (as defined in code):")
    print(f"  Width: {container_width:.3f}m")
    print(f"  Height: {container_height:.3f}m")
    print(f"  Depth/Length: {container_length:.3f}m")
    
    # Check which mapping makes sense
    print(f"\nMapping analysis:")
    print(f"Current assumption - X=width, Y=height, Z=depth:")
    print(f"  X fits in width? {max_x:.3f} <= {container_width:.3f}? {max_x <= container_width}")
    print(f"  Y fits in height? {max_y:.3f} <= {container_height:.3f}? {max_y <= container_height}")
    print(f"  Z fits in depth? {max_z:.3f} <= {container_length:.3f}? {max_z <= container_length}")
    
    # Alternative mappings
    print(f"\nAlternative mapping - X=depth, Y=width, Z=height:")
    print(f"  X fits in depth? {max_x:.3f} <= {container_length:.3f}? {max_x <= container_length}")
    print(f"  Y fits in width? {max_y:.3f} <= {container_width:.3f}? {max_y <= container_width}")
    print(f"  Z fits in height? {max_z:.3f} <= {container_height:.3f}? {max_z <= container_height}")
    
    print(f"\nAlternative mapping - X=width, Y=depth, Z=height:")
    print(f"  X fits in width? {max_x:.3f} <= {container_width:.3f}? {max_x <= container_width}")
    print(f"  Y fits in depth? {max_y:.3f} <= {container_length:.3f}? {max_y <= container_length}")
    print(f"  Z fits in height? {max_z:.3f} <= {container_height:.3f}? {max_z <= container_height}")
    
    # Test with a simple container to understand py3dbp coordinate system
    print(f"\n" + "="*50)
    print("TESTING PY3DBP COORDINATE SYSTEM")
    print("="*50)
    
    packer = Packer()
    
    # Create a small test container with known dimensions
    test_container = Bin("test", 3.0, 2.0, 5.0, 1000)  # width=3, height=2, depth=5
    packer.add_bin(test_container)
    
    print(f"Test container created:")
    print(f"  Width: {test_container.width}")
    print(f"  Height: {test_container.height}")
    print(f"  Depth: {test_container.depth}")
    
    # Add items to see how they're positioned
    test_item1 = Item("test1", 1.0, 1.0, 1.0, 1.0)  # 1x1x1 cube
    test_item2 = Item("test2", 1.0, 1.0, 1.0, 1.0)  # another 1x1x1 cube
    
    packer.add_item(test_item1)
    packer.add_item(test_item2)
    
    packer.pack()
    
    print(f"\nTest packing results:")
    for i, item in enumerate(test_container.items):
        print(f"Item {i+1}: position {item.position}, dimensions W={item.width} H={item.height} D={item.depth}")
    
    # Now let's figure out the correct container outline for our visualization
    print(f"\n" + "="*50)
    print("CORRECT CONTAINER OUTLINE CALCULATION")
    print("="*50)
    
    # Based on the data, determine what the container outline should be
    if max_x <= container_width and max_y <= container_height and max_z <= container_length:
        print("Current mapping is correct: X=width, Y=height, Z=depth")
        correct_container_x = container_width
        correct_container_y = container_height
        correct_container_z = container_length
        x_label = f"Width ({container_width:.3f}m)"
        y_label = f"Height ({container_height:.3f}m)"
        z_label = f"Depth ({container_length:.3f}m)"
    elif max_x <= container_length and max_y <= container_width and max_z <= container_height:
        print("Correct mapping: X=depth, Y=width, Z=height")
        correct_container_x = container_length
        correct_container_y = container_width
        correct_container_z = container_height
        x_label = f"Depth ({container_length:.3f}m)"
        y_label = f"Width ({container_width:.3f}m)"
        z_label = f"Height ({container_height:.3f}m)"
    elif max_x <= container_width and max_y <= container_length and max_z <= container_height:
        print("Correct mapping: X=width, Y=depth, Z=height")
        correct_container_x = container_width
        correct_container_y = container_length
        correct_container_z = container_height
        x_label = f"Width ({container_width:.3f}m)"
        y_label = f"Depth ({container_length:.3f}m)"
        z_label = f"Height ({container_height:.3f}m)"
    else:
        print("ERROR: No mapping fits!")
        return
    
    print(f"Correct container outline should be:")
    print(f"  X: 0 to {correct_container_x:.3f} ({x_label})")
    print(f"  Y: 0 to {correct_container_y:.3f} ({y_label})")
    print(f"  Z: 0 to {correct_container_z:.3f} ({z_label})")
    
    return correct_container_x, correct_container_y, correct_container_z, x_label, y_label, z_label

if __name__ == "__main__":
    debug_container_orientation()
