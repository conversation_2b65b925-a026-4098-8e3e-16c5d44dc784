#!/usr/bin/env python3
"""
Debug script to understand py3dbp coordinate system
"""

from py3dbp import Packer, Bin, Item
import pandas as pd

def debug_coordinate_system():
    """Debug the coordinate system and dimensions."""
    
    print("="*80)
    print("DEBUGGING PY3DBP COORDINATE SYSTEM")
    print("="*80)
    
    # Create a simple test
    packer = Packer()
    
    # Container dimensions from our code
    container_length = 12.032
    container_width = 2.352
    container_height = 2.698
    
    print(f"Container created with:")
    print(f"  Width: {container_width}")
    print(f"  Height: {container_height}")
    print(f"  Depth: {container_length}")
    
    # Create container - check parameter order
    container = Bin("test", container_width, container_height, container_length, 100000)
    packer.add_bin(container)
    
    # Item dimensions (converted from inches to meters)
    item_length = 31.1 * 0.0254  # 0.79m
    item_width = 29.92 * 0.0254  # 0.76m  
    item_height = 25.6 * 0.0254  # 0.65m
    
    print(f"\nItem dimensions:")
    print(f"  Length: {item_length:.3f}m")
    print(f"  Width: {item_width:.3f}m")
    print(f"  Height: {item_height:.3f}m")
    
    # Add a few items to see the pattern
    for i in range(5):
        item = Item(f"test_item_{i}", item_width, item_height, item_length, 10.0)
        packer.add_item(item)
        print(f"Item {i} created with W={item_width:.3f}, H={item_height:.3f}, D={item_length:.3f}")
    
    packer.pack()
    
    print(f"\nPacked items:")
    for i, item in enumerate(container.items):
        print(f"Item {i}:")
        print(f"  Position: {item.position}")
        print(f"  Dimensions: W={item.width}, H={item.height}, D={item.depth}")
        if hasattr(item, 'get_dimension'):
            dims = item.get_dimension()
            print(f"  Rotated dims: {dims}")
    
    # Check container actual dimensions
    print(f"\nContainer actual dimensions:")
    print(f"  Width: {container.width}")
    print(f"  Height: {container.height}")
    print(f"  Depth: {container.depth}")
    
    # Now check our actual data
    print(f"\n" + "="*50)
    print("CHECKING ACTUAL PACKING DATA")
    print("="*50)
    
    df = pd.read_excel('items_to_pack.xlsx', sheet_name='Packing_Results')
    
    print(f"Total items in data: {len(df)}")
    print(f"Item dimensions from data:")
    print(f"  Width: {df['actual_width_m'].iloc[0]:.3f}m")
    print(f"  Height: {df['actual_height_m'].iloc[0]:.3f}m") 
    print(f"  Depth: {df['actual_depth_m'].iloc[0]:.3f}m")
    
    print(f"\nPosition ranges:")
    print(f"  X: {df['position_x'].min():.3f} to {df['position_x'].max():.3f}")
    print(f"  Y: {df['position_y'].min():.3f} to {df['position_y'].max():.3f}")
    print(f"  Z: {df['position_z'].min():.3f} to {df['position_z'].max():.3f}")
    
    # Calculate actual space used
    max_x = df['position_x'].max() + df['actual_width_m'].iloc[0]
    max_y = df['position_y'].max() + df['actual_height_m'].iloc[0]
    max_z = df['position_z'].max() + df['actual_depth_m'].iloc[0]
    
    print(f"\nActual space used:")
    print(f"  X extent: {max_x:.3f}m (container width: {container_width:.3f}m)")
    print(f"  Y extent: {max_y:.3f}m (container height: {container_height:.3f}m)")
    print(f"  Z extent: {max_z:.3f}m (container depth: {container_length:.3f}m)")
    
    # Check if we have the coordinate mapping wrong
    print(f"\nCoordinate mapping analysis:")
    print(f"If X=width, Y=height, Z=depth:")
    print(f"  X utilization: {(max_x/container_width)*100:.1f}%")
    print(f"  Y utilization: {(max_y/container_height)*100:.1f}%") 
    print(f"  Z utilization: {(max_z/container_length)*100:.1f}%")
    
    # Check alternative mapping
    print(f"\nAlternative mapping - if Z is height:")
    print(f"  X utilization: {(max_x/container_length)*100:.1f}%")
    print(f"  Y utilization: {(max_y/container_width)*100:.1f}%")
    print(f"  Z utilization: {(max_z/container_height)*100:.1f}%")
    
    # Volume check
    item_volume = df['actual_width_m'].iloc[0] * df['actual_height_m'].iloc[0] * df['actual_depth_m'].iloc[0]
    total_item_volume = item_volume * len(df)
    container_volume = container_width * container_height * container_length
    
    print(f"\nVolume analysis:")
    print(f"  Single item volume: {item_volume:.6f} m³")
    print(f"  Total items volume: {total_item_volume:.3f} m³")
    print(f"  Container volume: {container_volume:.3f} m³")
    print(f"  Calculated utilization: {(total_item_volume/container_volume)*100:.2f}%")

if __name__ == "__main__":
    debug_coordinate_system()
