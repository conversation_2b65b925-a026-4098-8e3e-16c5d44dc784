#!/usr/bin/env python3
"""
Analysis script for container packing results
"""

import pandas as pd
import numpy as np

def analyze_packing_results():
    """Analyze the packing results from the Excel file."""
    
    # Read the packing results
    df = pd.read_excel('items_to_pack.xlsx', sheet_name='Packing_Results')
    
    print("="*80)
    print("DETAILED PACKING ANALYSIS")
    print("="*80)
    
    # Basic statistics
    total_items = len(df)
    print(f"\nBASIC STATISTICS:")
    print(f"Total items packed: {total_items}")
    print(f"Total volume: {df['volume_m3'].sum():.3f} m³")
    print(f"Total weight: {df['weight_kg'].sum():.2f} kg")
    
    # Position analysis
    print(f"\nPOSITION ANALYSIS:")
    print(f"X-axis range: {df['position_x'].min():.2f} to {df['position_x'].max():.2f} m")
    print(f"Y-axis range: {df['position_y'].min():.2f} to {df['position_y'].max():.2f} m") 
    print(f"Z-axis range: {df['position_z'].min():.2f} to {df['position_z'].max():.2f} m")
    
    # Rotation analysis
    print(f"\nROTATION ANALYSIS:")
    rotation_counts = df['rotation_type'].value_counts().sort_index()
    for rot_type, count in rotation_counts.items():
        percentage = (count / total_items) * 100
        print(f"Rotation type {rot_type}: {count} items ({percentage:.1f}%)")
    
    # Layer analysis (assuming items are stacked in layers)
    print(f"\nLAYER ANALYSIS:")
    # Group by Z position to find layers
    z_positions = df['position_z'].round(2).unique()
    z_positions.sort()
    
    print(f"Number of distinct Z-levels: {len(z_positions)}")
    for i, z_pos in enumerate(z_positions[:10]):  # Show first 10 layers
        items_at_level = len(df[df['position_z'].round(2) == z_pos])
        print(f"Level {i+1} (Z={z_pos:.2f}m): {items_at_level} items")
    
    if len(z_positions) > 10:
        print(f"... and {len(z_positions) - 10} more levels")
    
    # Efficiency metrics
    print(f"\nEFFICIENCY METRICS:")
    container_volume = 12.032 * 2.352 * 2.698  # 40HQ container
    volume_utilization = (df['volume_m3'].sum() / container_volume) * 100
    print(f"Volume utilization: {volume_utilization:.2f}%")
    
    # Spacing analysis
    print(f"\nSPACING ANALYSIS:")
    # Calculate gaps between items
    df_sorted_x = df.sort_values('position_x')
    x_gaps = []
    for i in range(len(df_sorted_x) - 1):
        current_item = df_sorted_x.iloc[i]
        next_item = df_sorted_x.iloc[i + 1]
        gap = next_item['position_x'] - (current_item['position_x'] + current_item['actual_width_m'])
        if gap > 0.001:  # Only consider significant gaps
            x_gaps.append(gap)
    
    if x_gaps:
        print(f"Average gap in X-direction: {np.mean(x_gaps):.3f} m")
        print(f"Maximum gap in X-direction: {np.max(x_gaps):.3f} m")
    else:
        print("No significant gaps found in X-direction")
    
    # Packing pattern analysis
    print(f"\nPACKING PATTERN:")
    # Check if items are arranged in a regular grid
    unique_x = len(df['position_x'].round(3).unique())
    unique_y = len(df['position_y'].round(3).unique())
    unique_z = len(df['position_z'].round(3).unique())
    
    print(f"Unique X positions: {unique_x}")
    print(f"Unique Y positions: {unique_y}")
    print(f"Unique Z positions: {unique_z}")
    
    # Calculate theoretical grid
    item_width = df['actual_width_m'].iloc[0]
    item_height = df['actual_height_m'].iloc[0]
    item_depth = df['actual_depth_m'].iloc[0]
    
    theoretical_x = int(12.032 / item_width)
    theoretical_y = int(2.352 / item_height)
    theoretical_z = int(2.698 / item_depth)
    theoretical_total = theoretical_x * theoretical_y * theoretical_z
    
    print(f"\nTHEORETICAL ANALYSIS:")
    print(f"Item dimensions: {item_width:.3f} × {item_height:.3f} × {item_depth:.3f} m")
    print(f"Theoretical fit: {theoretical_x} × {theoretical_y} × {theoretical_z} = {theoretical_total} items")
    print(f"Actual packed: {total_items} items")
    print(f"Packing efficiency: {(total_items / theoretical_total) * 100:.1f}% of theoretical maximum")

if __name__ == "__main__":
    analyze_packing_results()
