{"cells": [{"cell_type": "code", "execution_count": 24, "id": "f65e6835", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from py3dbp import Packer, Bin, Item\n", "\n", "def read_items_from_excel(file_path):\n", "    \"\"\"Reads item data from the specified Excel file.\"\"\"\n", "    try:\n", "        df = pd.read_excel(file_path, sheet_name= 'items_to_pack')\n", "        items_data = []\n", "        \n", "        # Iterate through each row to create item instances\n", "        # We create multiple instances of each type to attempt to fill the container\n", "        for index, row in df.iterrows():\n", "                item_name = str(row['name']) # Ensure name is string\n", "                length = float(row['length'])\n", "                width = float(row['width'])\n", "                height = float(row['height'])\n", "                weight = float(row['weight'])\n", "\n", "                # Basic validation\n", "                if length <= 0 or width <= 0 or height <= 0 or weight <= 0:\n", "                    print(f\"Warning: Skipping item '{item_name}' (row {index+2}) due to non-positive dimensions or weight.\")\n", "                    continue\n", "\n", "                # Create multiple instances of this item type\n", "                for i in range(500):\n", "                     # Give unique ID but keep original name identifier\n", "                    # py3dbp requires unique names for items added to packer\n", "                    items_data.append(Item(f\"{item_name}_instance_{i+1}\", width, length, height, weight)) # Note: py3dbp Item uses W, L, H order\n", "                print(items_data)\n", "\n", "        if not items_data:\n", "            print(\"No valid items found in the Excel file after processing.\")\n", "\n", "        return items_data, df # Return the list of item instances and the original dataframe\n", "\n", "    except FileNotFoundError:\n", "        print(f\"Error: Input file not found at {file_path}\")\n", "        return None, None\n", "    except ValueError as ve:\n", "        print(f\"Error reading Excel file: {ve}\")\n", "        return None, None\n", "    except Exception as e:\n", "        print(f\"An unexpected error occurred while reading Excel: {e}\")\n", "        return None, None"]}, {"cell_type": "code", "execution_count": 25, "id": "6df4d444", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["An unexpected error occurred while reading Excel: 'length'\n"]}], "source": ["items_data, df = read_items_from_excel('items_to_pack.xlsx')\n", "df"]}], "metadata": {"kernelspec": {"display_name": "ml", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}