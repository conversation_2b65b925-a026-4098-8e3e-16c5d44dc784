#!/usr/bin/env python3
"""
Test script to examine all available attributes of packed items in py3dbp
"""

from py3dbp import Packer, Bin, Item
import pandas as pd

def test_py3dbp_attributes():
    """Test what attributes are available on packed items."""

    # Create a simple test case
    packer = Packer()

    # Create a small container
    container = Bin("test_container", 5.0, 5.0, 5.0, 100.0)
    packer.add_bin(container)

    # Add items with different dimensions to force different rotations
    item1 = Item("item1_rect", 1.0, 0.5, 0.3, 1.0)  # Rectangular item
    item2 = Item("item2_cube", 0.5, 0.5, 0.5, 0.5)  # Cube item
    item3 = Item("item3_long", 2.0, 0.3, 0.3, 1.0)  # Long item
    item4 = Item("item4_flat", 1.0, 1.0, 0.2, 1.0)  # Flat item
    item5 = Item("item5_tall", 0.3, 0.3, 1.5, 1.0)  # Tall item

    packer.add_item(item1)
    packer.add_item(item2)
    packer.add_item(item3)
    packer.add_item(item4)
    packer.add_item(item5)

    # Pack the items
    packer.pack()
    
    # Examine the packed items
    packed_items = container.items

    print("=== PY3DBP PACKED ITEM ATTRIBUTES ===")
    print(f"Number of packed items: {len(packed_items)}")

    # Examine all packed items to see different rotation types
    print(f"\n=== ALL PACKED ITEMS WITH ROTATION DETAILS ===")
    for i, item in enumerate(packed_items):
        print(f"\nItem {i+1}: {item.name}")
        print(f"  Original dimensions (W×H×D): {item.width} × {item.height} × {item.depth}")
        print(f"  Position (X,Y,Z): {item.position}")
        print(f"  Rotation type: {item.rotation_type}")

        # Try to understand what rotation_type means
        if hasattr(item, 'get_dimension'):
            try:
                dims = item.get_dimension()
                print(f"  Actual dimensions after rotation: {dims}")
            except:
                print(f"  Could not get rotated dimensions")

    if packed_items:
        item = packed_items[0]  # Get first packed item

        print(f"\n=== DETAILED EXAMINATION OF FIRST ITEM ===")
        print(f"Examining first packed item: {item.name}")

        # Check common attributes
        attributes_to_check = [
            'name', 'width', 'height', 'depth', 'weight',
            'position', 'rotation_type', 'rotation',
            'x', 'y', 'z'
        ]

        print(f"\n=== DETAILED ATTRIBUTE VALUES ===")
        for attr in attributes_to_check:
            if hasattr(item, attr):
                value = getattr(item, attr)
                print(f"{attr}: {value} (type: {type(value)})")
            else:
                print(f"{attr}: NOT AVAILABLE")
        
        # Check if there are any rotation-related attributes
        rotation_attrs = [attr for attr in dir(item) if 'rotat' in attr.lower()]
        print(f"\nRotation-related attributes: {rotation_attrs}")
        
        # Check if there are any orientation-related attributes  
        orientation_attrs = [attr for attr in dir(item) if 'orient' in attr.lower()]
        print(f"Orientation-related attributes: {orientation_attrs}")
        
        # Print position details
        if hasattr(item, 'position'):
            print(f"\nPosition details:")
            print(f"  Position: {item.position}")
            print(f"  Position type: {type(item.position)}")
            if hasattr(item.position, '__len__') and len(item.position) >= 3:
                print(f"  X: {item.position[0]}")
                print(f"  Y: {item.position[1]}")
                print(f"  Z: {item.position[2]}")
        
        # Check for rotation type
        if hasattr(item, 'rotation_type'):
            print(f"\nRotation details:")
            print(f"  Rotation type: {item.rotation_type}")
            print(f"  Rotation type value: {type(item.rotation_type)}")
            
        # Print all non-private attributes and their values
        print(f"\n=== ALL PUBLIC ATTRIBUTES ===")
        for attr in sorted(dir(item)):
            if not attr.startswith('_'):
                try:
                    value = getattr(item, attr)
                    if not callable(value):
                        print(f"{attr}: {value}")
                except:
                    print(f"{attr}: <could not access>")

if __name__ == "__main__":
    test_py3dbp_attributes()
