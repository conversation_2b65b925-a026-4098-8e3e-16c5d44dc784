#!/usr/bin/env python3
"""
Test script to force different rotations in py3dbp
"""

from py3dbp import Packer, Bin, Item

def test_rotations():
    """Test different rotation scenarios."""
    
    # Test 1: Force rotation by container constraints
    print("=== TEST 1: FORCING ROTATIONS ===")
    packer = Packer()
    
    # Create a narrow container that forces rotation
    container = Bin("narrow_container", 1.0, 3.0, 3.0, 100.0)  # W=1, H=3, D=3
    packer.add_bin(container)
    
    # Add a wide item that must be rotated to fit
    wide_item = Item("wide_item", 2.0, 0.5, 0.5, 1.0)  # W=2, H=0.5, D=0.5
    packer.add_item(wide_item)
    
    # Pack
    packer.pack()
    
    packed_items = container.items
    print(f"Packed items: {len(packed_items)}")
    
    for item in packed_items:
        print(f"Item: {item.name}")
        print(f"  Original: W={item.width}, H={item.height}, D={item.depth}")
        print(f"  Position: {item.position}")
        print(f"  Rotation type: {item.rotation_type}")
        if hasattr(item, 'get_dimension'):
            dims = item.get_dimension()
            print(f"  Rotated dims: {dims}")
    
    # Test 2: Multiple items with different orientations
    print(f"\n=== TEST 2: MULTIPLE ORIENTATIONS ===")
    packer2 = Packer()
    
    # Create container
    container2 = Bin("test_container", 2.0, 2.0, 4.0, 100.0)  # W=2, H=2, D=4
    packer2.add_bin(container2)
    
    # Add items that might need different orientations
    items = [
        Item("item1", 1.5, 0.5, 0.5, 1.0),  # Might need rotation
        Item("item2", 0.5, 1.5, 0.5, 1.0),  # Different orientation
        Item("item3", 0.5, 0.5, 1.5, 1.0),  # Yet another orientation
        Item("item4", 1.0, 1.0, 3.0, 1.0),  # Long item
    ]
    
    for item in items:
        packer2.add_item(item)
    
    packer2.pack()
    
    packed_items2 = container2.items
    print(f"Packed items: {len(packed_items2)}")
    
    for item in packed_items2:
        print(f"Item: {item.name}")
        print(f"  Original: W={item.width}, H={item.height}, D={item.depth}")
        print(f"  Position: {item.position}")
        print(f"  Rotation type: {item.rotation_type}")
        if hasattr(item, 'get_dimension'):
            dims = item.get_dimension()
            print(f"  Rotated dims: {dims}")
    
    # Test 3: Check what rotation types mean
    print(f"\n=== TEST 3: ROTATION TYPE MEANINGS ===")
    
    # Based on 3D bin packing literature, rotation types typically represent:
    rotation_meanings = {
        0: "No rotation (W×H×D)",
        1: "Rotate around Z-axis (H×W×D)", 
        2: "Rotate around Y-axis (D×H×W)",
        3: "Rotate around X-axis (W×D×H)",
        4: "Rotate around Z then Y (H×D×W)",
        5: "Rotate around Z then X (D×W×H)"
    }
    
    print("Typical rotation type meanings:")
    for rot_type, meaning in rotation_meanings.items():
        print(f"  {rot_type}: {meaning}")
    
    # Test if we can manually check rotation effects
    print(f"\n=== MANUAL ROTATION VERIFICATION ===")
    if packed_items2:
        for item in packed_items2:
            original_dims = [float(item.width), float(item.height), float(item.depth)]
            if hasattr(item, 'get_dimension'):
                rotated_dims = [float(d) for d in item.get_dimension()]
                print(f"Item {item.name}:")
                print(f"  Original: {original_dims}")
                print(f"  Rotated:  {rotated_dims}")
                print(f"  Rotation type: {item.rotation_type}")
                
                # Check if dimensions match expected rotation
                if item.rotation_type == 0 and original_dims == rotated_dims:
                    print(f"  ✓ No rotation confirmed")
                elif item.rotation_type == 1 and rotated_dims == [original_dims[1], original_dims[0], original_dims[2]]:
                    print(f"  ✓ Z-axis rotation confirmed (W↔H)")
                elif item.rotation_type == 2 and rotated_dims == [original_dims[2], original_dims[1], original_dims[0]]:
                    print(f"  ✓ Y-axis rotation confirmed (W↔D)")
                elif item.rotation_type == 3 and rotated_dims == [original_dims[0], original_dims[2], original_dims[1]]:
                    print(f"  ✓ X-axis rotation confirmed (H↔D)")
                else:
                    print(f"  ? Rotation pattern not recognized")

if __name__ == "__main__":
    test_rotations()
