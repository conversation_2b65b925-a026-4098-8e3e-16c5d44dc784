import xlwings as xl
import pandas as pd
from collections import defaultdict
import pickle

class ExcelIntegration():

    @staticmethod
    def populate_workbook(path : str, input_data_map : pd.DataFrame):
        print("Starting populate_workbook function")
        print(f"Opening workbook at path: {path}")

        # Read your data (assuming it's in Excel already)
        try:
            wb = xl.Book(path)
            if 'UI' not in wb.sheet_names:
                wb.sheets.add('UI')
            if 'Data' not in wb.sheet_names:
                wb.sheets.add('Data')

        except:
            wb = xl.Book()
            wb.save(path)
            wb.sheets.add('UI')
            wb.sheets.add('Data') # Use the path parameter instead of hardcoded path

        print("Workbook opened successfully")

        ws_data = wb.sheets['Data']  # Sheet with your table
        print("Data sheet accessed successfully")
        ws_data.used_range.clear_contents()
        ws_data.range('A1').value = input_data_map.columns.to_list()
        ws_data.range('A2').value = input_data_map.values.tolist()

        # Read the table into pandas
        data_range = ws_data.range('A1').expand()  # Adjust as needed
        df = data_range.options(pd.DataFrame, header=1, index=False).value

        print("Creating data dictionaries")

        # Create a dictionary of factories by planner
        planner_factories = defaultdict(list)
        for _, row in df.iterrows():
            if row['factory'] not in planner_factories[row['planner']]:
                planner_factories[row['planner']].append(row['factory'])

        # Create a dictionary of items by factory (not by planner)
        # Store both description and itemid
        factory_items = defaultdict(list)
        factory_itemids = {}  # To map descriptions to itemids

        for _, row in df.iterrows():
            # Store the description for dropdown display
            if row['desc'] not in factory_items[row['factory']]:
                factory_items[row['factory']].append(row['desc'])

                # Initialize nested dict if needed
                if row['factory'] not in factory_itemids:
                    factory_itemids[row['factory']] = {}

                # Map description to itemid
                factory_itemids[row['factory']][row['desc']] = row['itemid']

        # Convert to regular dicts
        planner_factories = dict(planner_factories)
        factory_items = dict(factory_items)
        factory_itemids = dict(factory_itemids)
        print("Data dictionaries created successfully")

        # Create a new sheet for dropdown data
        print("Setting up DropdownData sheet")
        try:
            ws_dropdowns = wb.sheets['DropdownData']
            ws_dropdowns.clear()
            print("Existing DropdownData sheet cleared")
        except:
            ws_dropdowns = wb.sheets.add('DropdownData')
            print("New DropdownData sheet created")

        # Set up the data structure for named ranges
        current_col = 1

        # 1. Create Planners list
        print("Creating Planners list")
        planners = list(planner_factories.keys())
        print(f"Planners: {planners}")
        ws_dropdowns.range((1, current_col)).value = [['Planners']] + [[p] for p in planners]
        print("Planners list written to sheet")

        # Create named range for planners
        print("Creating named range for planners")
        planner_range = ws_dropdowns.range((2, current_col), (len(planners) + 1, current_col))
        print(f"Range address: {planner_range.address}")
        try:
            # First check if the name already exists and delete it
            try:
                wb.names['Planners'].delete()
                print("Deleted existing 'Planners' named range")
            except:
                print("No existing 'Planners' named range to delete")

            # Use Excel formula approach instead of direct API
            formula = f"=DropdownData!{planner_range.address}"
            print(f"Using formula: {formula}")

            # Use a different method to create the named range
            # Use the workbook's app instead of trying to get the active app
            app = wb.app
            app.api.Names.Add(Name="Planners", RefersTo=formula)
            print("Named range 'Planners' created successfully using Excel API")
        except Exception as e:
            print(f"Error creating named range 'Planners': {e}")
            print("Continuing without creating named range")

        current_col += 2

        # 2. Create factory lists for each planner
        print("Creating factory lists for each planner")
        factory_start_row = 1
        for planner in planners:
            print(f"Processing planner: {planner}")
            factories = planner_factories[planner]
            print(f"Factories for {planner}: {factories}")

            # Clean planner name for range name (remove all non-alphanumeric chars)
            import re
            clean_planner = re.sub(r'[^a-zA-Z0-9]', '', planner)
            print(f"Clean planner name: {clean_planner}")

            # Write factory header and data
            ws_dropdowns.range((factory_start_row, current_col)).value = f"{clean_planner}_Factories"
            factory_data = [[f] for f in factories]
            ws_dropdowns.range((factory_start_row + 1, current_col)).value = factory_data
            print(f"Factory data written to sheet for {planner}")

            # Create named range for this planner's factories
            factory_range = ws_dropdowns.range((factory_start_row + 1, current_col),
                                            (factory_start_row + len(factories), current_col))
            print(f"Range address for {clean_planner}_Factories: {factory_range.address}")
            try:
                # First check if the name already exists and delete it
                range_name = f'{clean_planner}_Factories'
                try:
                    wb.names[range_name].delete()
                    print(f"Deleted existing '{range_name}' named range")
                except:
                    print(f"No existing '{range_name}' named range to delete")

                # Use Excel formula approach instead of direct API
                formula = f"=DropdownData!{factory_range.address}"
                print(f"Using formula: {formula}")

                # Use the workbook's app
                app = wb.app
                app.api.Names.Add(Name=range_name, RefersTo=formula)
                print(f"Named range '{range_name}' created successfully using Excel API")
            except Exception as e:
                print(f"Error creating named range '{clean_planner}_Factories': {e}")
                print("Continuing without creating named range")

            current_col += 1

        # 3. Create item lists for each factory (not dependent on planner)
        print("Creating item lists for each factory")
        item_start_row = max(len(planners), max(len(factories) for factories in planner_factories.values())) + 5
        print(f"Item start row: {item_start_row}")
        current_col = 1

        # Get a unique list of all factories
        all_factories = set()
        for factories in planner_factories.values():
            all_factories.update(factories)

        # Sort factories for consistent order
        all_factories = sorted(all_factories)

        for factory in all_factories:
            print(f"Processing items for factory: {factory}")
            items = factory_items[factory]

            # Normalize factory name by removing all spaces and special characters
            import re
            clean_factory = re.sub(r'[^a-zA-Z0-9]', '', factory)
            print(f"Clean factory name: {clean_factory}")

            # Create unique range name for this factory
            range_name = f"{clean_factory}_Items"
            print(f"Range name: {range_name}")

            # Write items header and data
            ws_dropdowns.range((item_start_row, current_col)).value = range_name

            # Create list of items (description only)
            item_data = [[item] for item in items]
            print(f"Number of items: {len(item_data)}")

            if item_data:  # Only if there are items
                ws_dropdowns.range((item_start_row + 1, current_col)).value = item_data
                print(f"Item data written to sheet for {factory}")

                # Create named range for this factory
                item_range = ws_dropdowns.range((item_start_row + 1, current_col),
                                            (item_start_row + len(item_data), current_col))
                print(f"Range address for {range_name}: {item_range.address}")
                try:
                    # First check if the name already exists and delete it
                    try:
                        wb.names[range_name].delete()
                        print(f"Deleted existing '{range_name}' named range")
                    except:
                        print(f"No existing '{range_name}' named range to delete")

                    # Use Excel formula approach instead of direct API
                    formula = f"=DropdownData!{item_range.address}"
                    print(f"Using formula: {formula}")

                    # Use the workbook's app
                    app = wb.app
                    app.api.Names.Add(Name=range_name, RefersTo=formula)
                    print(f"Named range '{range_name}' created successfully using Excel API")
                except Exception as e:
                    print(f"Error creating named range '{range_name}': {e}")
                    print("Continuing without creating named range")
            else:
                print(f"No items for {factory}")

            current_col += 1

        print("All named ranges created successfully")

        ws_ui = wb.sheets['UI']

    @staticmethod
    def get_selections(path: str) -> dict:
        wb = xl.Book(path)
        ws_ui = wb.sheets['UI']
        planner = ws_ui.range('A2').value
        factory = ws_ui.range('B2').value
        items_selected = ws_ui.range('C5').expand('down').value
        itemids_selected = ws_ui.range('D5').expand('down').value
        return {'planner': planner, 'factory': factory, 'items_selected': items_selected, 'itemids_selected': itemids_selected}


    @staticmethod
    def populate_output(path: str, output_data: pd.DataFrame):
        wb = xl.Book(path)
        ws_output = wb.sheets['Output']
        ws_output.clear()
        ws_output.range('C2').value = output_data.columns.to_list()
        ws_output.range('C3').value = output_data.values
        ws_output.range('A3:A' + str(len(output_data) + 2)).value = output_data.index.to_list()
        #formatting the output
        ws_output.range('A2:Z2').color = (198, 217, 241)
        ws_output.range('A2:Z2').font.bold = True
        ws_output.range('A3:A' + str(len(output_data) + 2)).color = (198, 217, 241)
        ws_output.range('A3:A' + str(len(output_data) + 2)).font.bold = True
        ws_output.range('C2').expand('right').font.bold = True
        ws_output.range('C2').expand('right').color = (198, 217, 241)
        ws_output.activate()


if __name__ == '__main__':
    input_data_map = pickle.load(open('./optimization/input_data_map.pkl', 'rb'))
    print(input_data_map)
    ExcelIntegration.populate_workbook('./optimization/PO_Optimization.xlsm', input_data_map)
