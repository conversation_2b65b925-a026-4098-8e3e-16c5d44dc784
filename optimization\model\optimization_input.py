from pydantic import BaseModel, Field, field_validator, model_validator

class OptimizationInput(BaseModel):

    forecast_data: dict[tuple[int, str], int] = Field(..., description="A dictionary containing the forecast data for each item. The keys of the outer dictionary are the item ids and the keys of the inner dictionary are the week names. The values of the inner dictionary are the forecasted quantities.")
    forecast_periods: list[str] = Field(..., description="A list containing the names of the forecast periods.")
    variable_periods: list[str] = Field(..., description="A list containing the names of the variable periods.")
    forecast_items: list[int] = Field(..., description="A list containing the ids of the forecast items.")
    beginning_inventory: dict[tuple[int, str], int]  = Field(..., description="A dictionary containing the beginning inventory for each item. The keys of the dictionary are the item ids and the values are the beginning inventory quantities.")   
    gross_margin_data: dict[int, float] = Field(..., description="A dictionary containing the gross margin for each item. The keys of the dictionary are the item ids and the values are the gross margin amounts.")
    storage_cost_per_cuft: float = Field(..., description="The storage cost per cubic foot.")
    storage_volume: dict[int, float] = Field(..., description="A dictionary containing the storage volume for each item. The keys of the dictionary are the item ids and the values are the storage volumes.")
    container_batch_size: int = Field(..., description="The container batch size.The no of units of a group of items that can fill a container")
    arrival_data: dict[tuple[int, str], int] = Field(..., description="A dictionary containing the arrival data for each item. The keys of the outer dictionary are the item ids and the keys of the inner dictionary are the week names. The values of the inner dictionary are the arrival quantities.")