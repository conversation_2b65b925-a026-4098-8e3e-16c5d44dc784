from pyomo.environ import * 
from model.optimization_input import OptimizationInput
import logging
import pandas as pd

class PurchaseOrderOptimization(): 
    ''' This class will accept inputs from the user and then create and solve thecl optimization model.'''
    def __init__(self, InputData: OptimizationInput):
        self.InputData = InputData
        self.initialize_model(InputData)
        
    def initialize_model(self, InputData: OptimizationInput):
        # Initialize the model
        self.model = ConcreteModel()
        self.model.forecast_period = Set (initialize = self.InputData.forecast_periods)
        self.model.variable_periods = Set (initialize = self.InputData.variable_periods)
        self.model.forecast_items  = Set (initialize = self.InputData.forecast_items)
        self.model.start_week = Set(initialize=[self.InputData.forecast_periods[0]])
        self.model.forecasts = Param( self.model.forecast_items, self.model.forecast_period, within = NonNegativeIntegers, initialize = InputData.forecast_data)
        
        self.model.beginning_inv = Param(self.model.forecast_items, self.model.start_week, initialize=InputData.beginning_inventory)
        self.model.arrivals = Param( self.model.forecast_items, self.model.forecast_period, within = NonNegativeIntegers, initialize=self.InputData.arrival_data)
        self.model.orders_to_place = Var(self.model.forecast_items, self.model.variable_periods, within=NonNegativeIntegers, initialize=0 )
        self.model.num_batches = Var(self.model.variable_periods, within=NonNegativeIntegers, initialize=0)
        self.model.container_batch_size = Param(initialize=InputData.container_batch_size)
        
        self.model.permutation_matix = Var(self.model.forecast_period, self.model.forecast_period, domain=Boolean, initialize=0)

        self.model.shifted_arrivals = Expression(self.model.forecast_items, self.model.forecast_period, rule=lambda m, i, t: sum(m.arrivals[i, t_prime] * m.permutation_matix[t, t_prime] for t_prime in m.forecast_period))

        def calc_ending_inv(m, i, t):
            # For the first period
            if t == m.forecast_period.first():
                return m.beginning_inv[i,t] + m.arrivals[i, t] - m.forecasts[i, t]
            # For periods where variables are not defined (first 4 periods)
            elif t not in m.variable_periods:
                return m.ending_inv[i, m.forecast_period.prev(t)] + m.arrivals[i, t] - m.forecasts[i, t]
            # For periods where variables are defined ( all but the first 8 periods )
            else:
                return m.ending_inv[i, m.forecast_period.prev(t)] + m.orders_to_place[i, t] + m.shifted_arrivals[i, t] - m.forecasts[i, t]
    
        self.model.ending_inv = Expression(self.model.forecast_items, self.model.forecast_period, rule=calc_ending_inv)
        self.model.storage_volume = Expression(self.model.forecast_items, self.model.forecast_period, rule=lambda m, i, t: m.ending_inv[i, t] * InputData.storage_volume[i])
        self.model.storage_costs = Expression(self.model.forecast_items, self.model.forecast_period, rule=lambda m, i, t: m.storage_volume[i, t] * InputData.storage_cost_per_cuft)
        self.model.gross_margin = Param(self.model.forecast_items, initialize=InputData.gross_margin_data)
        self.model.net_contribution = Expression(self.model.forecast_items, self.model.forecast_period, rule=lambda m, i, t: m.gross_margin[i] * m.forecasts[i, t] - m.storage_costs[i, t])
        self.model.obj = Objective(rule=lambda m: sum(m.net_contribution[i, t] for i in m.forecast_items for t in m.forecast_period), sense=maximize, name='Maximize_Net_Contribution')
        
        def order_constraint_rule(m, t):
            return sum(m.orders_to_place[i, t] for i in m.forecast_items)  == m.num_batches[t] * m.container_batch_size
        self.model.order_constraint = Constraint(self.model.variable_periods, rule=order_constraint_rule)
        
        def ending_inv_constraint_rule(m, i, t):
            return m.ending_inv[i, t] >= 1
        self.model.ending_inv_constraint = Constraint(self.model.forecast_items, self.model.variable_periods, rule=ending_inv_constraint_rule)

        def permutation_matrix_row_constraint(m, t):
            return sum(m.permutation_matix[t, t_prime] for t_prime in m.forecast_period) == 1
        self.model.permutation_matrix_row_constraint = Constraint(self.model.forecast_period, rule=permutation_matrix_row_constraint)

        def permutation_matrix_col_constraint(m, t_prime):
            return sum(m.permutation_matix[t, t_prime] for t in m.forecast_period) == 1
        self.model.permutation_matrix_col_constraint = Constraint(self.model.forecast_period, rule=permutation_matrix_col_constraint)

        #constrain the first 8 periods of the permutation matrix so the first 8 periods are not shifted
        def permutation_matrix_first_8_periods_constraint(m, t, t_prime):
            if t not in m.variable_periods and t_prime not in m.variable_periods :
                if t == t_prime:
                    return m.permutation_matix[t, t_prime] == 1
                else:
                    return m.permutation_matix[t, t_prime] == 0
            else:
                return Constraint.Skip
        self.model.permutation_matrix_first_8_periods_constraint = Constraint(self.model.forecast_period, self.model.forecast_period, rule=permutation_matrix_first_8_periods_constraint)

    def optimize(self):
        # solve and update model variables if solver fails
        
        solver = SolverFactory('highs')
        res=solver.solve(self.model)
        self.SolverStatus = res.solver.status
        self.SolverTerminationCondition = res.solver.termination_condition
        self.SolverTerminationMessage = res.solver.termination_message
        if res.solver.status != SolverStatus.ok:    
            raise Exception(f'Solver failed with termination condition: {res.solver.termination_condition} \n status: {res.solver.status} \n message: {res.solver.termination_message}' )
        
    def get_results(self):

        '''To get the results as a pandas dataframe. The dataframe should have the week no as the columns and the rows 
        should be the item id. For each item id we need the forecast, arrivals, orders to place, ending inventory'''

        forecast_data = {t: {i: self.model.forecasts[i, t] for i in self.model.forecast_items} for t in self.model.forecast_period}
        arrivals_data = {t: {i: self.model.arrivals[i, t] for i in  self.model.forecast_items} for t in self.model.forecast_period}
        shifted_arrivals_data = {t: {i: value( self.model.shifted_arrivals[i, t]) for i in self.model.forecast_items} for t in self.model.forecast_period}
        orders_to_place_data = {t: {i: value( self.model.orders_to_place[i, t]) for i in self.model.forecast_items} for t in self.model.variable_periods}
        ending_inv_data = {t: {i: value ( self.model.ending_inv[i, t]) for i in self.model.forecast_items} for t in self.model.forecast_period}

        df_forecast = pd.DataFrame(forecast_data).rename_axis(index='itemid', columns='week').assign(type='forecast').set_index('type', append=True)
        df_arrivals = pd.DataFrame(arrivals_data).rename_axis(index='itemid', columns='week').assign(type='arrivals').set_index('type', append=True)
        df_shifted_arrivals = pd.DataFrame(shifted_arrivals_data).rename_axis(index='itemid', columns='week').assign(type='shifted_arrivals').set_index('type', append=True)
        df_orders_to_place = pd.DataFrame(orders_to_place_data).rename_axis(index='itemid', columns='week').assign(type='orders_to_place').set_index('type', append=True)
        df_ending_inv = pd.DataFrame(ending_inv_data).rename_axis(index='itemid', columns='week').assign(type='ending_inv').set_index('type', append=True)
        consolidated_report_df = pd.concat([df_forecast, df_arrivals, df_shifted_arrivals, df_orders_to_place, df_ending_inv], axis=0)
        return consolidated_report_df.swaplevel(0,1)







