# Requirements for PO Optimization Project
# Only includes packages NOT in standard Anaconda installation

# Excel integration
xlwings>=0.30.0

# Database connectivity
turbodbc>=4.5.0
pyodbc>=4.0.0

# Optimization modeling
pyomo>=6.6.0

# Optimization solver
highspy>=1.5.0

# Data validation
pydantic>=2.0.0

# GUI components (for PO_Prioritization scripts)
tkcalendar>=1.6.0

# YAML parsing (PyYAML is included in Anaconda but ensuring compatibility)
PyYAML>=6.0

# Excel file handling (openpyxl is included in Anaconda but ensuring compatibility)
openpyxl>=3.1.0
